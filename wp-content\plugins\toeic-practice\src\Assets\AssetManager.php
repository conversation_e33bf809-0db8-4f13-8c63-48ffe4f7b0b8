<?php
/**
 * Asset Manager
 *
 * @package ToeicPractice\Assets
 */

namespace ToeicPractice\Assets;

/**
 * Asset Manager Class
 * 
 * Handles enqueuing of CSS and JavaScript files for both
 * frontend and admin areas.
 */
class AssetManager {
    
    /**
     * Plugin URL
     *
     * @var string
     */
    private $plugin_url;
    
    /**
     * Plugin version
     *
     * @var string
     */
    private $version;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->plugin_url = TOEIC_PRACTICE_PLUGIN_URL;
        $this->version = TOEIC_PRACTICE_VERSION;
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueueFrontendAssets() {
        // Only enqueue on pages that need them
        if (!$this->shouldEnqueueFrontendAssets()) {
            return;
        }
        
        // Enqueue CSS
        $this->enqueueFrontendStyles();
        
        // Enqueue JavaScript
        $this->enqueueFrontendScripts();
    }
    
    /**
     * Enqueue admin assets
     *
     * @param string $hook_suffix Current admin page hook suffix
     */
    public function enqueueAdminAssets($hook_suffix) {
        // Only enqueue on plugin admin pages
        if (!$this->shouldEnqueueAdminAssets($hook_suffix)) {
            return;
        }
        
        // Enqueue CSS
        $this->enqueueAdminStyles();
        
        // Enqueue JavaScript
        $this->enqueueAdminScripts();
    }
    
    /**
     * Enqueue frontend styles
     */
    private function enqueueFrontendStyles() {
        //wp_enqueue_style(
        //    'toeic-practice-frontend',
        //    $this->plugin_url . 'assets/css/frontend.css',
        //    [],
        //    $this->version
        //);
    }
    
    /**
     * Enqueue frontend scripts
     */
    private function enqueueFrontendScripts() {
        // Enqueue jQuery if not already loaded
        wp_enqueue_script('jquery');
    }
    
    /**
     * Enqueue admin styles
     */
    private function enqueueAdminStyles() {
        wp_enqueue_style(
            'toeic-practice-admin',
            $this->plugin_url . 'assets/css/admin.css',
            [],
            $this->version
        );
        
        // WordPress admin styles
        wp_enqueue_style('wp-color-picker');
        
        // Select2 for enhanced dropdowns
        wp_enqueue_style(
            'select2',
            'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
            [],
            '4.1.0'
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    private function enqueueAdminScripts() {
        // WordPress media uploader
        wp_enqueue_media();
        
        // WordPress color picker
        wp_enqueue_script('wp-color-picker');
        
        // Select2 for enhanced dropdowns
        wp_enqueue_script(
            'select2',
            'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
            ['jquery'],
            '4.1.0',
            true
        );
        
        // Main admin script
        wp_enqueue_script(
            'toeic-practice-admin',
            $this->plugin_url . 'assets/js/admin.js',
            ['jquery', 'wp-color-picker', 'media-upload'],
            $this->version,
            true
        );
        
        // Question management script
        wp_enqueue_script(
            'toeic-practice-questions',
            $this->plugin_url . 'assets/js/admin-question.js',
            ['jquery', 'toeic-practice-admin'],
            $this->version,
            true
        );

        // Test management script
        wp_enqueue_script(
            'toeic-practice-test',
            $this->plugin_url . 'assets/js/admin-test.js',
            ['jquery', 'toeic-practice-admin'],
            $this->version,
            true
        );

        // Vocabulary management script
        wp_enqueue_script(
            'toeic-practice-vocabulary',
            $this->plugin_url . 'assets/js/admin-vocabulary.js',
            ['jquery', 'toeic-practice-admin'],
            $this->version,
            true
        );

        // Pronunciation topic management script
        wp_enqueue_script(
            'toeic-practice-pronunciation-topic',
            $this->plugin_url . 'assets/js/admin-pronunciation-topic.js',
            ['jquery', 'select2', 'toeic-practice-admin'],
            $this->version,
            true
        );

        // AI suggestions management script
        wp_enqueue_script(
            'toeic-practice-ai-suggestions',
            $this->plugin_url . 'assets/js/admin-ai-suggestions.js',
            ['jquery', 'jquery-ui-sortable', 'toeic-practice-admin'],
            $this->version,
            true
        );

        // Statistics and charts
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js',
            [],
            '3.9.1',
            true
        );
        
        wp_enqueue_script(
            'toeic-practice-charts',
            $this->plugin_url . 'assets/js/charts.js',
            ['chart-js', 'jquery'],
            $this->version,
            true
        );
        
        // Localize admin script
        wp_localize_script('toeic-practice-admin', 'toeicPracticeAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'adminUrl' => admin_url(),
            'nonce' => wp_create_nonce('toeic_practice_admin_nonce'),
            'strings' => [
                'confirm_delete' => __('Are you sure you want to delete this item?', 'toeic-practice'),
                'bulk_delete_confirm' => __('Are you sure you want to delete the selected items?', 'toeic-practice'),
                'save_success' => __('Settings saved successfully.', 'toeic-practice'),
                'save_error' => __('Error saving settings. Please try again.', 'toeic-practice'),
                'upload_audio' => __('Select Audio File', 'toeic-practice'),
                'upload_image' => __('Select Image', 'toeic-practice'),
                'remove_media' => __('Remove', 'toeic-practice'),
                'drag_to_reorder' => __('Drag to reorder', 'toeic-practice'),
                'remove_suggestion' => __('Remove suggestion', 'toeic-practice'),
                'suggestion_placeholder' => __('Enter your AI suggestion text here...', 'toeic-practice'),
                'no_suggestions' => __('Please add at least one suggestion before saving.', 'toeic-practice')
            ],
            'mediaUpload' => [
                'title' => __('Select Media', 'toeic-practice'),
                'button' => __('Use this media', 'toeic-practice'),
                'multiple' => false
            ]
        ]);
    }
    
    /**
     * Check if frontend assets should be enqueued
     *
     * @return bool
     */
    private function shouldEnqueueFrontendAssets() {
        global $post;
        
        // Always enqueue on TOEIC practice pages
        if (is_singular(['toeic_test', 'toeic_question'])) {
            return true;
        }
        
        // Check if shortcode is present in post content
        if ($post && has_shortcode($post->post_content, 'toeic_practice')) {
            return true;
        }
        
        if ($post && has_shortcode($post->post_content, 'toeic_test')) {
            return true;
        }
        
        if ($post && has_shortcode($post->post_content, 'toeic_progress')) {
            return true;
        }
        
        // Check if we're on a page with TOEIC practice widgets
        if (is_active_widget(false, false, 'toeic_practice_widget')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if admin assets should be enqueued
     *
     * @param string $hook_suffix Current admin page hook suffix
     * @return bool
     */
    private function shouldEnqueueAdminAssets($hook_suffix) {
        // Plugin admin pages
        $plugin_pages = [
            'toplevel_page_toeic-practice',
            'toeic-practice_page_toeic-practice-tests',
            'toeic-practice_page_toeic-practice-questions',
            'toeic-practice_page_toeic-practice-results',
            'toeic-practice_page_toeic-practice-vocabulary',
            'toeic-practice_page_toeic-practice-settings',
            'toeic-practice_page_toeic-practice-pronunciation-topics',
            'toeic-practice_page_toeic-practice-ai-suggestions'
        ];
        
        if (in_array($hook_suffix, $plugin_pages)) {
            return true;
        }
        
        // Post edit pages for TOEIC post types
        if (in_array($hook_suffix, ['post.php', 'post-new.php'])) {
            global $post_type;
            if (in_array($post_type, ['toeic_test', 'toeic_question'])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Register and enqueue block editor assets
     */
    public function enqueueBlockEditorAssets() {
        wp_enqueue_script(
            'toeic-practice-blocks',
            $this->plugin_url . 'assets/js/blocks.js',
            ['wp-blocks', 'wp-element', 'wp-editor'],
            $this->version,
            true
        );
        
        wp_enqueue_style(
            'toeic-practice-blocks',
            $this->plugin_url . 'assets/css/blocks.css',
            ['wp-edit-blocks'],
            $this->version
        );
    }
    
    /**
     * Get asset URL
     *
     * @param string $path Asset path relative to assets directory
     * @return string Full asset URL
     */
    public function getAssetUrl($path) {
        return $this->plugin_url . 'assets/' . ltrim($path, '/');
    }
    
    /**
     * Get versioned asset URL
     *
     * @param string $path Asset path relative to assets directory
     * @return string Full asset URL with version parameter
     */
    public function getVersionedAssetUrl($path) {
        return $this->getAssetUrl($path) . '?ver=' . $this->version;
    }
}
