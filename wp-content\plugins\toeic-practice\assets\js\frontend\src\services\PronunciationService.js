/**
 * Pronunciation Service
 * Handles pronunciation grading and related API calls
 */
import ajaxService from './AjaxService.js';

class PronunciationService {
    constructor() {
        this.ajaxService = ajaxService;
    }

    /**
     * Submit audio recording for pronunciation grading
     * @param {Blob} audioBlob - The recorded audio blob
     * @param {Object} params - Practice parameters
     * @returns {Promise<Object>} - Grading results
     */
    async gradeRecording(audioBlob, params = {}) {
        try {
            // Create data object with audio file and parameters
            const data = {
                audio: audioBlob,
                topic_id: params.topic_id || '',
                item_id: params.item_id || '',
                item_type: params.item_type || 'question'
            };
            
            // Use AjaxService post method which handles FormData automatically
            return await this.ajaxService.post('grade_pronunciation', data);
            
        } catch (error) {
            console.error('Error grading recording:', error);
            throw error;
        }
    }

    /**
     * Get pronunciation topics
     * @returns {Promise<Array>} - List of pronunciation topics
     */
    async getTopics() {
        try {
            return await this.ajaxService.post('get_pronunciation_topics');
        } catch (error) {
            console.error('Error fetching topics:', error);
            throw error;
        }
    }

    /**
     * Get topic items (questions and vocabulary)
     * @param {number} topicId - Topic ID
     * @returns {Promise<Array>} - List of topic items
     */
    async getTopicItems(topicId) {
        try {
            return await this.ajaxService.post('get_topic_items', {
                topic_id: topicId
            });
        } catch (error) {
            console.error('Error fetching topic items:', error);
            throw error;
        }
    }

    /**
     * Save pronunciation practice result
     * @param {Object} resultData - Practice result data
     * @returns {Promise<Object>} - Save result
     */
    async saveResult(resultData) {
        try {
            return await this.ajaxService.post('save_pronunciation_result', {
                result_data: JSON.stringify(resultData)
            });
        } catch (error) {
            console.error('Error saving result:', error);
            throw error;
        }
    }

    /**
     * Get user's pronunciation history
     * @param {Object} filters - Filter options
     * @returns {Promise<Array>} - List of practice results
     */
    async getHistory(filters = {}) {
        try {
            const data = {};
            
            // Add filters if provided
            if (filters.topic_id) {
                data.topic_id = filters.topic_id;
            }
            if (filters.limit) {
                data.limit = filters.limit;
            }
            if (filters.offset) {
                data.offset = filters.offset;
            }
            
            return await this.ajaxService.post('get_pronunciation_history', data);
        } catch (error) {
            console.error('Error fetching history:', error);
            throw error;
        }
    }

    /**
     * Get suggestions for pronunciation practice
     * @returns {Promise<Array>} - List of suggestions
     */
    async getSuggestions({prompt, subject}) {
        try {
            let data = {
                "prompt": prompt,
                "subject": subject
            }
            return await this.ajaxService.post('get_pronunciation_suggestions', data);
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            throw error;
        }
    }
}

// Export for use in other modules
export default PronunciationService;
