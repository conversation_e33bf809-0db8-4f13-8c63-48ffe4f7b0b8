/**
 * Admin Pronunciation Topic JavaScript
 * 
 * Handles admin functionality for pronunciation topics
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize
    init();
    
    function init() {
        initSortable();
        initSelect2();
        initAddItems();
        initRemoveItems();
        initDeleteConfirmation();
        initBulkActions();
    }
    
    /**
     * Initialize sortable functionality for topic items
     */
    function initSortable() {
        if ($('#topic-items-list').length) {
            $('#topic-items-list').sortable({
                handle: '.handle',
                placeholder: 'ui-state-highlight',
                tolerance: 'pointer',
                cursor: 'move',
                opacity: 0.8,
                update: function(event, ui) {
                    updateItemIndexes();
                }
            });
        }
    }
    
    /**
     * Initialize Select2 dropdowns with AJAX search
     */
    function initSelect2() {
        // Initialize questions Select2
        $('#available-questions').select2({
            placeholder: 'Search for questions...',
            allowClear: true,
            minimumInputLength: 0,
            ajax: {
                url: toeicPracticeAdmin.ajaxUrl,
                dataType: 'json',
                delay: 300, // Debounce delay
                data: function (params) {
                    return {
                        action: 'toeic_search_pronunciation_questions',
                        search: params.term || '',
                        page: params.page || 1,
                        nonce: toeicPracticeAdmin.nonce
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    
                    if (data.success) {
                        return {
                            results: data.data.results,
                            pagination: {
                                more: data.data.pagination.more
                            }
                        };
                    } else {
                        console.error('Error loading questions:', data.data);
                        return {
                            results: [],
                            pagination: {
                                more: false
                            }
                        };
                    }
                },
                cache: true
            },
            templateResult: function(item) {
                if (item.loading) {
                    return item.text;
                }
                return $('<span>' + escapeHtml(item.text) + '</span>');
            },
            templateSelection: function(item) {
                return item.text || item.id;
            }
        });
        
        // Initialize vocabulary Select2
        $('#available-vocabulary').select2({
            placeholder: 'Search for vocabulary...',
            allowClear: true,
            minimumInputLength: 0,
            ajax: {
                url: toeicPracticeAdmin.ajaxUrl,
                dataType: 'json',
                delay: 300, // Debounce delay
                data: function (params) {
                    return {
                        action: 'toeic_search_pronunciation_vocabulary',
                        search: params.term || '',
                        page: params.page || 1,
                        nonce: toeicPracticeAdmin.nonce
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    
                    if (data.success) {
                        return {
                            results: data.data.results,
                            pagination: {
                                more: data.data.pagination.more
                            }
                        };
                    } else {
                        console.error('Error loading vocabulary:', data.data);
                        return {
                            results: [],
                            pagination: {
                                more: false
                            }
                        };
                    }
                },
                cache: true
            },
            templateResult: function(item) {
                if (item.loading) {
                    return item.text;
                }
                return $('<span>' + escapeHtml(item.text) + '</span>');
            },
            templateSelection: function(item) {
                return item.text || item.id;
            }
        });
    }
    
    /**
     * Update item indexes after sorting
     */
    function updateItemIndexes() {
        $('#topic-items-list .topic-item').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('input[name*="[type]"]').attr('name', 'topic_items[' + index + '][type]');
            $(this).find('input[name*="[id]"]').attr('name', 'topic_items[' + index + '][id]');
        });
    }
    
    /**
     * Initialize add items functionality
     */
    function initAddItems() {
        // Add questions
        $('#add-questions').on('click', function() {
            var selectedData = $('#available-questions').select2('data');
            
            if (selectedData.length === 0) {
                alert('Please select at least one question to add.');
                return;
            }
            
            selectedData.forEach(function(item) {
                var itemId = item.id;
                var itemTitle = item.text;
                var itemType = item.type || 'question';
                
                // Check if item already exists
                if (isItemAlreadyAdded(itemType, itemId)) {
                    return; // Skip if already added
                }
                
                addTopicItem(itemType, itemId, itemTitle);
            });
            
            // Clear selection
            $('#available-questions').val(null).trigger('change');
            updateItemIndexes();
        });
        
        // Add vocabulary
        $('#add-vocabulary').on('click', function() {
            var selectedData = $('#available-vocabulary').select2('data');
            
            if (selectedData.length === 0) {
                alert('Please select at least one vocabulary item to add.');
                return;
            }
            
            selectedData.forEach(function(item) {
                var itemId = item.id;
                var itemTitle = item.text;
                var itemType = item.type || 'vocabulary';
                
                // Check if item already exists
                if (isItemAlreadyAdded(itemType, itemId)) {
                    return; // Skip if already added
                }
                
                addTopicItem(itemType, itemId, itemTitle);
            });
            
            // Clear selection
            $('#available-vocabulary').val(null).trigger('change');
            updateItemIndexes();
        });
    }
    
    /**
     * Check if item is already added to topic
     */
    function isItemAlreadyAdded(itemType, itemId) {
        var exists = false;
        $('#topic-items-list .topic-item').each(function() {
            var existingType = $(this).find('input[name*="[type]"]').val();
            var existingId = $(this).find('input[name*="[id]"]').val();
            
            if (existingType === itemType && existingId === itemId) {
                exists = true;
                return false; // Break loop
            }
        });
        return exists;
    }
    
    /**
     * Add topic item to the list
     */
    function addTopicItem(itemType, itemId, itemTitle) {
        var index = $('#topic-items-list .topic-item').length;
        var itemHtml = `
            <div class="topic-item" data-index="${index}">
                <div class="item-header">
                    <span class="dashicons dashicons-menu handle"></span>
                    <strong>${escapeHtml(itemTitle)}</strong>
                    <span class="item-type">(${capitalizeFirst(itemType)})</span>
                    <button type="button" class="button-link remove-item" style="color: #a00; float: right;">
                        Remove
                    </button>
                </div>
                <input type="hidden" name="topic_items[${index}][type]" value="${itemType}">
                <input type="hidden" name="topic_items[${index}][id]" value="${itemId}">
            </div>
        `;
        
        $('#topic-items-list').append(itemHtml);
    }
    
    /**
     * Initialize remove items functionality
     */
    function initRemoveItems() {
        $(document).on('click', '.remove-item', function() {
            if (confirm(toeicPracticeAdmin.strings.confirm_delete || 'Are you sure you want to remove this item?')) {
                var topicItem = $(this).closest('.topic-item');
                
                // Since Select2 dropdowns are AJAX-driven, we don't need to add items back
                // They will be available again when the user searches
                topicItem.remove();
                updateItemIndexes();
            }
        });
    }
    
    /**
     * Initialize delete confirmation for topic listing page
     */
    function initDeleteConfirmation() {
        $('.delete-topic').on('click', function(e) {
            if (!confirm(toeicPracticeAdmin.strings.confirm_delete)) {
                e.preventDefault();
                return false;
            }
        });
    }
    
    /**
     * Initialize bulk actions
     */
    function initBulkActions() {
        // Select all checkbox
        $('#cb-select-all-1').on('change', function() {
            $('input[name="topic_ids[]"]').prop('checked', this.checked);
        });
        
        // Individual checkboxes
        $(document).on('change', 'input[name="topic_ids[]"]', function() {
            var totalCheckboxes = $('input[name="topic_ids[]"]').length;
            var checkedCheckboxes = $('input[name="topic_ids[]"]:checked').length;
            
            $('#cb-select-all-1').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
        
        // Bulk action form submission
        $('form').on('submit', function(e) {
            var action = $('select[name="action"]').val();
            
            if (action === 'delete') {
                var checkedItems = $('input[name="topic_ids[]"]:checked').length;
                
                if (checkedItems > 0) {
                    if (!confirm(toeicPracticeAdmin.strings.bulk_delete_confirm)) {
                        e.preventDefault();
                        return false;
                    }
                }
            }
        });
    }
    
    /**
     * Escape HTML characters
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    
    /**
     * Capitalize first letter
     */
    function capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
});
