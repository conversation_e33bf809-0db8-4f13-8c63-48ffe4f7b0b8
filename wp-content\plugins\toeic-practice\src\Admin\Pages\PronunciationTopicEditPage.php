<?php
/**
 * Pronunciation Topic Edit Page
 *
 * @package ToeicPractice\Admin\Pages
 */

namespace ToeicPractice\Admin\Pages;

use ToeicPractice\Plugin;

/**
 * Pronunciation Topic Edit Page Class
 * 
 * Handles adding and editing pronunciation topics in admin area
 */
class PronunciationTopicEditPage {
    
    /**
     * Topic data
     *
     * @var array
     */
    private $topic = [];
    
    /**
     * Topic ID
     *
     * @var int
     */
    private $topic_id = 0;
    
    /**
     * Is edit mode
     *
     * @var bool
     */
    private $is_edit = false;
    
    /**
     * Available questions
     *
     * @var array
     */
    private $available_questions = [];
    
    /**
     * Available vocabulary
     *
     * @var array
     */
    private $available_vocabulary = [];
    
    /**
     * Topic items
     *
     * @var array
     */
    private $topic_items = [];
    
    /**
     * Display the topic edit page
     */
    public function display() {
        // Get topic ID if in edit mode
        $this->topic_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $this->is_edit = $this->topic_id > 0;
        
        // Process form submission
        if (isset($_POST['submit_topic'])) {
            $this->processFormSubmission();
        }
        
        // Load topic data if in edit mode
        if ($this->is_edit) {
            $this->loadTopicData();
        } else {
            // Default values for new topic
            $this->topic = [
                'title' => '',
                'description' => '',
                'status' => 'draft'
            ];
        }
        
        // Load available questions and vocabulary
        $this->loadAvailableItems();
        
        // Load topic items if in edit mode
        if ($this->is_edit) {
            $this->loadTopicItems();
        }
        
        // Display the form
        $this->renderForm();
    }
    
    /**
     * Load topic data
     */
    private function loadTopicData() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'toeic_pronunciation_topics';
        $this->topic = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE id = %d", $this->topic_id),
            ARRAY_A
        );
        
        if (!$this->topic) {
            wp_die(__('Topic not found.', 'toeic-practice'));
        }
    }
    
    /**
     * Load available questions and vocabulary
     */
    private function loadAvailableItems() {
        global $wpdb;
        
        // Load questions
        $questions_table = $wpdb->prefix . 'toeic_questions';
        $this->available_questions = $wpdb->get_results(
            "SELECT id, title, question_type FROM $questions_table WHERE status = 'published' ORDER BY title",
            ARRAY_A
        );
        
        // Load vocabulary
        $vocabulary_table = $wpdb->prefix . 'toeic_vocabulary';
        $this->available_vocabulary = $wpdb->get_results(
            "SELECT id, word, vi_translate FROM $vocabulary_table ORDER BY word",
            ARRAY_A
        );
    }
    
    /**
     * Load topic items
     */
    private function loadTopicItems() {
        global $wpdb;
        
        $items_table = $wpdb->prefix . 'toeic_pronunciation_topic_items';
        $questions_table = $wpdb->prefix . 'toeic_questions';
        $vocabulary_table = $wpdb->prefix . 'toeic_vocabulary';
        
        // Get topic items with details
        $sql = "
            SELECT 
                ti.id,
                ti.item_type,
                ti.item_id,
                ti.sort_order,
                CASE 
                    WHEN ti.item_type = 'question' THEN q.title
                    WHEN ti.item_type = 'vocabulary' THEN v.word
                END as item_title,
                CASE 
                    WHEN ti.item_type = 'question' THEN q.question_type
                    WHEN ti.item_type = 'vocabulary' THEN v.vi_translate
                END as item_detail
            FROM $items_table ti
            LEFT JOIN $questions_table q ON ti.item_type = 'question' AND ti.item_id = q.id
            LEFT JOIN $vocabulary_table v ON ti.item_type = 'vocabulary' AND ti.item_id = v.id
            WHERE ti.topic_id = %d
            ORDER BY ti.sort_order, ti.id
        ";
        
        $this->topic_items = $wpdb->get_results(
            $wpdb->prepare($sql, $this->topic_id),
            ARRAY_A
        );
    }
    
    /**
     * Process form submission
     */
    private function processFormSubmission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'], 'pronunciation_topic_edit')) {
            wp_die(__('Security check failed.', 'toeic-practice'));
        }
        
        // Sanitize input data
        $title = sanitize_text_field($_POST['title']);
        $description = sanitize_textarea_field($_POST['description']);
        $status = sanitize_text_field($_POST['status']);
        
        // Validate required fields
        if (empty($title)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Title is required.', 'toeic-practice') . '</p></div>';
            });
            return;
        }
        
        // Prepare data
        $data = [
            'title' => $title,
            'description' => $description,
            'status' => $status,
            'updated_at' => current_time('mysql')
        ];
        
        global $wpdb;
        $topics_table = $wpdb->prefix . 'toeic_pronunciation_topics';
        
        if ($this->is_edit) {
            // Update existing topic
            $result = $wpdb->update(
                $topics_table,
                $data,
                ['id' => $this->topic_id],
                ['%s', '%s', '%s', '%s'],
                ['%d']
            );
            
            if ($result !== false) {
                $this->saveTopicItems();
                $message = __('Topic updated successfully.', 'toeic-practice');
            } else {
                $message = __('Error updating topic.', 'toeic-practice');
            }
        } else {
            // Create new topic
            $data['created_at'] = current_time('mysql');
            
            $result = $wpdb->insert(
                $topics_table,
                $data,
                ['%s', '%s', '%s', '%s', '%s']
            );
            
            if ($result) {
                $this->topic_id = $wpdb->insert_id;
                $this->is_edit = true;
                $this->saveTopicItems();
                $message = __('Topic created successfully.', 'toeic-practice');
            } else {
                $message = __('Error creating topic.', 'toeic-practice');
            }
        }
        
        // Redirect with message
        $redirect_url = add_query_arg([
            'page' => 'toeic-practice-pronunciation-topics',
            'action' => 'edit',
            'id' => $this->topic_id,
            'message' => urlencode($message)
        ], admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Save topic items
     */
    private function saveTopicItems() {
        global $wpdb;
        $items_table = $wpdb->prefix . 'toeic_pronunciation_topic_items';
        
        // Delete existing items
        $wpdb->delete($items_table, ['topic_id' => $this->topic_id], ['%d']);
        
        // Insert new items
        $sort_order = 0;
        if (isset($_POST['topic_items']) && is_array($_POST['topic_items'])) {
            foreach ($_POST['topic_items'] as $item) {
                if (empty($item['type']) || empty($item['id'])) {
                    continue;
                }
            
                $wpdb->insert(
                    $items_table,
                    [
                        'topic_id' => $this->topic_id,
                        'item_type' => sanitize_text_field($item['type']),
                        'item_id' => intval($item['id']),
                        'sort_order' => $sort_order++,
                        'created_at' => current_time('mysql'),
                        'updated_at' => current_time('mysql')
                    ],
                    ['%d', '%s', '%d', '%d', '%s', '%s']
                );
            }
        } else {
            // Remove all items
            $wpdb->delete($items_table, ['topic_id' => $this->topic_id], ['%d']);
        }
    }
    
    /**
     * Render the form
     */
    private function renderForm() {
        ?>
        <div class="wrap">
            <h1><?php echo $this->is_edit ? __('Edit Pronunciation Topic', 'toeic-practice') : __('Add New Pronunciation Topic', 'toeic-practice'); ?></h1>
            
            <?php if (isset($_GET['message'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php echo esc_html(urldecode($_GET['message'])); ?></p>
                </div>
            <?php endif; ?>
            
            <form method="post" id="pronunciation-topic-form">
                <?php wp_nonce_field('pronunciation_topic_edit'); ?>
                
                <div id="poststuff">
                    <div id="post-body" class="metabox-holder columns-2">
                        <div id="post-body-content">
                            <!-- Main Content -->
                            <div class="postbox">
                                <div class="postbox-header">
                                    <h2><?php _e('Topic Details', 'toeic-practice'); ?></h2>
                                </div>
                                <div class="inside">
                                    <table class="form-table">
                                        <tr>
                                            <th scope="row">
                                                <label for="title"><?php _e('Title', 'toeic-practice'); ?> <span class="description">(required)</span></label>
                                            </th>
                                            <td>
                                                <input type="text" id="title" name="title" value="<?php echo esc_attr($this->topic['title']); ?>" class="regular-text" required>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row">
                                                <label for="description"><?php _e('Description', 'toeic-practice'); ?></label>
                                            </th>
                                            <td>
                                                <textarea id="description" name="description" rows="4" class="large-text"><?php echo esc_textarea($this->topic['description']); ?></textarea>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Topic Items -->
                            <div class="postbox">
                                <div class="postbox-header">
                                    <h2><?php _e('Practice Items', 'toeic-practice'); ?></h2>
                                </div>
                                <div class="inside">
                                    <div id="topic-items-container">
                                        <div id="topic-items-list">
                                            <?php if (!empty($this->topic_items)): ?>
                                                <?php foreach ($this->topic_items as $index => $item): ?>
                                                    <div class="topic-item" data-index="<?php echo $index; ?>">
                                                        <div class="item-header">
                                                            <span class="dashicons dashicons-menu handle"></span>
                                                            <strong><?php echo esc_html($item['item_title']); ?></strong>
                                                            <span class="item-type">(<?php echo esc_html(ucfirst($item['item_type'])); ?>)</span>
                                                            <button type="button" class="button-link remove-item" style="color: #a00; float: right;">
                                                                <?php _e('Remove', 'toeic-practice'); ?>
                                                            </button>
                                                        </div>
                                                        <input type="hidden" name="topic_items[<?php echo $index; ?>][type]" value="<?php echo esc_attr($item['item_type']); ?>">
                                                        <input type="hidden" name="topic_items[<?php echo $index; ?>][id]" value="<?php echo esc_attr($item['item_id']); ?>">
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="add-items-section">
                                            <h4><?php _e('Add Items', 'toeic-practice'); ?></h4>
                                            
                                            <div class="add-questions">
                                                <label><?php _e('Add Questions:', 'toeic-practice'); ?></label>
                                                <select id="available-questions" multiple style="width: 100%;">
                                                    <!-- Options will be loaded via AJAX -->
                                                </select>
                                                <button type="button" id="add-questions" class="button"><?php _e('Add Selected Questions', 'toeic-practice'); ?></button>
                                            </div>
                                            
                                            <div class="add-vocabulary" style="margin-top: 20px;">
                                                <label><?php _e('Add Vocabulary:', 'toeic-practice'); ?></label>
                                                <select id="available-vocabulary" multiple style="width: 100%;">
                                                    <!-- Options will be loaded via AJAX -->
                                                </select>
                                                <button type="button" id="add-vocabulary" class="button"><?php _e('Add Selected Vocabulary', 'toeic-practice'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="postbox-container-1" class="postbox-container">
                            <!-- Publish Box -->
                            <div class="postbox">
                                <div class="postbox-header">
                                    <h2><?php _e('Publish', 'toeic-practice'); ?></h2>
                                </div>
                                <div class="inside">
                                    <div class="submitbox">
                                        <div class="misc-pub-section">
                                            <label for="status"><?php _e('Status:', 'toeic-practice'); ?></label>
                                            <select name="status" id="status">
                                                <option value="draft" <?php selected($this->topic['status'], 'draft'); ?>><?php _e('Draft', 'toeic-practice'); ?></option>
                                                <option value="published" <?php selected($this->topic['status'], 'published'); ?>><?php _e('Published', 'toeic-practice'); ?></option>
                                            </select>
                                        </div>
                                        
                                        <div class="major-publishing-actions">
                                            <div class="publishing-action">
                                                <?php submit_button($this->is_edit ? __('Update Topic', 'toeic-practice') : __('Create Topic', 'toeic-practice'), 'primary', 'submit_topic', false); ?>
                                            </div>
                                            <div class="clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <style>
        .topic-item {
            border: 1px solid #ddd;
            margin-bottom: 10px;
            padding: 10px;
            background: #f9f9f9;
            cursor: move;
        }
        .topic-item .item-header {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .topic-item .handle {
            cursor: move;
            color: #666;
        }
        .topic-item .item-type {
            color: #666;
            font-style: italic;
        }
        .add-items-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .add-questions, .add-vocabulary {
            margin-bottom: 15px;
        }
        .add-questions label, .add-vocabulary label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        #topic-items-list.ui-sortable .topic-item {
            cursor: move;
        }
        </style>
        <?php
    }
}
