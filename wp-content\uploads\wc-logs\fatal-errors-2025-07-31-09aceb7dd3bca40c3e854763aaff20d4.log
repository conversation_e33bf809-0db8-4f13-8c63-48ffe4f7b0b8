2025-07-31T04:12:04+00:00 CRITICAL Uncaught TypeError: call_user_func_array(): Argument #1 ($callback) must be a valid callback, cannot access private method ToeicPractice\Ajax\PronunciationHandler::handleGetSuggestions() in D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324 CONTEXT: {"error":{"type":1,"file":"D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php","line":324},"remote-logging":true,"backtrace":["#0 D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)n#1 D:\wamp64\www\toeictest\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#2 D:\wamp64\www\toeictest\wp-admin\admin-ajax.php(192): do_action('wp_ajax_get_pro...')n#3 {main}n  thrown"]}
2025-07-31T04:12:22+00:00 CRITICAL Uncaught TypeError: call_user_func_array(): Argument #1 ($callback) must be a valid callback, cannot access private method ToeicPractice\Ajax\PronunciationHandler::handleGetSuggestions() in D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324 CONTEXT: {"error":{"type":1,"file":"D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php","line":324},"remote-logging":true,"backtrace":["#0 D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)n#1 D:\wamp64\www\toeictest\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#2 D:\wamp64\www\toeictest\wp-admin\admin-ajax.php(192): do_action('wp_ajax_get_pro...')n#3 {main}n  thrown"]}
2025-07-31T04:41:14+00:00 CRITICAL syntax error, unexpected token "return" CONTEXT: {"error":{"type":4,"file":"D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php","line":310},"remote-logging":true,"backtrace":[{"file":"D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php","line":422,"function":"critical","class":"WC_Logger","type":"->"},{"function":"log_errors","class":"WooCommerce","type":"->"}]}
2025-07-31T04:43:15+00:00 CRITICAL syntax error, unexpected token "return" CONTEXT: {"error":{"type":4,"file":"D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php","line":310},"remote-logging":true,"backtrace":[{"file":"D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php","line":422,"function":"critical","class":"WC_Logger","type":"->"},{"function":"log_errors","class":"WooCommerce","type":"->"}]}
2025-07-31T04:45:16+00:00 CRITICAL syntax error, unexpected token "return" CONTEXT: {"error":{"type":4,"file":"D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php","line":310},"remote-logging":true,"backtrace":[{"file":"D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php","line":422,"function":"critical","class":"WC_Logger","type":"->"},{"function":"log_errors","class":"WooCommerce","type":"->"}]}
2025-07-31T04:47:17+00:00 CRITICAL syntax error, unexpected token "return" CONTEXT: {"error":{"type":4,"file":"D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php","line":310},"remote-logging":true,"backtrace":[{"file":"D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php","line":422,"function":"critical","class":"WC_Logger","type":"->"},{"function":"log_errors","class":"WooCommerce","type":"->"}]}
