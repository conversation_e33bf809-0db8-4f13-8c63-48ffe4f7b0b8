{"version": 3, "file": "index.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMA,MAAM;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,OAAA,EAA0B;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAL,MAAA;IACpB,IAAI,CAACC,OAAO,GAAG;MACXK,WAAW,EAAEL,OAAO,CAACK,WAAW,IAAI,wBAAwB;MAC5DC,IAAI,EAAEN,OAAO,CAACM,IAAI,IAAI,YAAY;MAClCC,IAAI,EAAEP,OAAO,CAACO,IAAI,IAAI,QAAQ;MAC9BC,UAAU,EAAER,OAAO,CAACQ,UAAU,IAAI;IACtC,CAAC;IAED,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,KAAK;IAEtB,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;;EAEA;AACJ;AACA;EAFI,OAAAC,YAAA,CAAAd,MAAA;IAAAe,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACH,SAAS,GAAGO,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACjB,OAAO,CAACK,WAAW,CAAC;;MAElE;MACA,IAAI,CAAC,IAAI,CAACI,SAAS,EAAE;QACjB,IAAI,CAACA,SAAS,GAAGO,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC9C,IAAI,CAACT,SAAS,CAACU,EAAE,GAAG,IAAI,CAACnB,OAAO,CAACK,WAAW;QAC5C,IAAI,CAACI,SAAS,CAACW,SAAS,GAAG,wBAAwB;QAEnD,IAAI,IAAI,CAACpB,OAAO,CAACQ,UAAU,EAAE;UACzB,IAAI,CAACC,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAC9C;QAEAN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACf,SAAS,CAAC;MAC7C;;MAEA;MACA,IAAI,CAACgB,YAAY,CAAC,CAAC;IACvB;;IAEA;AACJ;AACA;EAFI;IAAAX,GAAA;IAAAC,KAAA,EAGA,SAAAU,YAAYA,CAAA,EAAG;MACX,IAAI,CAACf,MAAM,GAAGM,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC3C,IAAI,CAACR,MAAM,CAACU,SAAS,mBAAAM,MAAA,CAAmB,IAAI,CAAC1B,OAAO,CAACO,IAAI,CAAE;;MAE3D;MACA,IAAMoB,OAAO,GAAGX,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC7CS,OAAO,CAACP,SAAS,GAAG,eAAe;MACnC,IAAI,CAACV,MAAM,CAACc,WAAW,CAACG,OAAO,CAAC;;MAEhC;MACA,IAAI,IAAI,CAAC3B,OAAO,CAACM,IAAI,EAAE;QACnB,IAAMsB,WAAW,GAAGZ,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACjDU,WAAW,CAACR,SAAS,GAAG,mBAAmB;QAC3CQ,WAAW,CAACC,WAAW,GAAG,IAAI,CAAC7B,OAAO,CAACM,IAAI;QAC3C,IAAI,CAACI,MAAM,CAACc,WAAW,CAACI,WAAW,CAAC;MACxC;;MAEA;MACA,IAAI,CAACnB,SAAS,CAACe,WAAW,CAAC,IAAI,CAACd,MAAM,CAAC;MACvC,IAAI,CAACoB,IAAI,CAAC,CAAC;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhB,GAAA;IAAAC,KAAA,EAKA,SAAAgB,IAAIA,CAAA,EAAc;MAAA,IAAbzB,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACZ,IAAIK,IAAI,EAAE;QACN,IAAI,CAAC0B,UAAU,CAAC1B,IAAI,CAAC;MACzB;MAEA,IAAI,CAACG,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MACpC,IAAI,CAACX,SAAS,GAAG,IAAI;MAErB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;EAFI;IAAAG,GAAA;IAAAC,KAAA,EAGA,SAAAe,IAAIA,CAAA,EAAG;MACH,IAAI,CAACrB,SAAS,CAACwB,KAAK,CAACC,OAAO,GAAG,MAAM;MACrC,IAAI,CAACvB,SAAS,GAAG,KAAK;MAEtB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAG,GAAA;IAAAC,KAAA,EAKA,SAAAiB,UAAUA,CAAC1B,IAAI,EAAE;MACb,IAAMsB,WAAW,GAAG,IAAI,CAAClB,MAAM,CAACyB,aAAa,CAAC,oBAAoB,CAAC;MAEnE,IAAIP,WAAW,EAAE;QACbA,WAAW,CAACC,WAAW,GAAGvB,IAAI;MAClC,CAAC,MAAM,IAAIA,IAAI,EAAE;QACb;QACA,IAAM8B,cAAc,GAAGpB,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACpDkB,cAAc,CAAChB,SAAS,GAAG,mBAAmB;QAC9CgB,cAAc,CAACP,WAAW,GAAGvB,IAAI;QACjC,IAAI,CAACI,MAAM,CAACc,WAAW,CAACY,cAAc,CAAC;MAC3C;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAtB,GAAA;IAAAC,KAAA,EAKA,SAAAsB,MAAMA,CAAA,EAAc;MAAA,IAAb/B,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACd,IAAI,IAAI,CAACU,SAAS,EAAE;QAChB,IAAI,CAACmB,IAAI,CAAC,CAAC;MACf,CAAC,MAAM;QACH,IAAI,CAACC,IAAI,CAACzB,IAAI,CAAC;MACnB;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAQ,GAAA;IAAAC,KAAA,EAOA,SAAAuB,OAAOA,CAACC,QAAQ,EAAe;MAAA,IAAAC,KAAA;MAAA,IAAblC,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACzB,OAAO,IAAIwC,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC1BF,KAAI,CAACT,IAAI,CAACzB,IAAI,CAAC;QAEfqC,UAAU,CAAC,YAAM;UACbH,KAAI,CAACV,IAAI,CAAC,CAAC;UACXY,OAAO,CAAC,CAAC;QACb,CAAC,EAAEH,QAAQ,CAAC;MAChB,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAzB,GAAA;IAAAC,KAAA,EAOA,SAAO6B,UAAUA,CAACC,OAAO,EAAgB;MAAA,IAAd7C,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnC;MACA,IAAI,OAAO4C,OAAO,KAAK,QAAQ,EAAE;QAC7BA,OAAO,GAAG7B,QAAQ,CAACmB,aAAa,CAACU,OAAO,CAAC;MAC7C;MAEA,IAAI,CAACA,OAAO,EAAE;QACVC,OAAO,CAACC,KAAK,CAAC,yCAAyC,CAAC;QACxD,OAAO,IAAI;MACf;;MAEA;MACA,IAAMC,QAAQ,GAAG,eAAe,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE1E;MACA,IAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACV,OAAO,CAAC;MACtD,IAAIQ,aAAa,CAACG,QAAQ,KAAK,QAAQ,EAAE;QACrCX,OAAO,CAACZ,KAAK,CAACuB,QAAQ,GAAG,UAAU;MACvC;;MAEA;MACA,IAAM/C,SAAS,GAAGO,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC/CT,SAAS,CAACU,EAAE,GAAG6B,QAAQ;MACvBvC,SAAS,CAACW,SAAS,GAAG,wBAAwB;MAC9CyB,OAAO,CAACrB,WAAW,CAACf,SAAS,CAAC;;MAE9B;MACA,OAAO,IAAIV,MAAM,CAAA0D,aAAA,CAAAA,aAAA,KACVzD,OAAO;QACVK,WAAW,EAAE2C,QAAQ;QACrBxC,UAAU,EAAE;MAAK,EACpB,CAAC;IACN;EAAC;AAAA;AAGL,iEAAeT,MAAM,E;;;;;;;;;;;;;;;;;;;;AC1MrB;AACA;AACA;AACA;AACA;AACA;AALA,IAMM2D,OAAO;EACT;AACJ;AACA;AACA;AACA;EACI,SAAAA,QAAYb,OAAO,EAAE;IAAAzC,eAAA,OAAAsD,OAAA;IACjB,IAAI,CAACb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACc,SAAS,GAAG,IAAI,CAACd,OAAO,CAACe,gBAAgB,CAAC,kBAAkB,CAAC;EACtE;;EAEA;AACJ;AACA;EAFI,OAAA/C,YAAA,CAAA6C,OAAA;IAAA5C,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH,IAAI,CAACiD,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC/B;;IAEA;AACJ;AACA;EAFI;IAAAhD,GAAA;IAAAC,KAAA,EAGA,SAAA8C,mBAAmBA,CAAA,EAAG;MAAA,IAAArB,KAAA;MAClB,IAAI,CAACmB,SAAS,CAACI,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BA,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAEzB,KAAI,CAAC0B,mBAAmB,CAACC,IAAI,CAAC3B,KAAI,CAAC,CAAC;MACvE,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA1B,GAAA;IAAAC,KAAA,EAKA,SAAAmD,mBAAmBA,CAACE,KAAK,EAAE;MACvB;MACA,IAAI,CAACT,SAAS,CAACI,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BA,IAAI,CAAC3C,SAAS,CAACgD,MAAM,CAAC,QAAQ,CAAC;MACnC,CAAC,CAAC;;MAEF;MACAD,KAAK,CAACE,aAAa,CAACjD,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IAC/C;;IAEA;AACJ;AACA;EAFI;IAAAR,GAAA;IAAAC,KAAA,EAGA,SAAA+C,oBAAoBA,CAAA,EAAG;MACnB;MACA,IAAMS,WAAW,GAAGjB,MAAM,CAACkB,QAAQ,CAACC,QAAQ;;MAE5C;MACA,IAAI,CAACd,SAAS,CAACI,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAMU,IAAI,GAAGV,IAAI,CAAC7B,aAAa,CAAC,GAAG,CAAC;QACpC,IAAIuC,IAAI,IAAIA,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC,KAAKJ,WAAW,EAAE;UACnDP,IAAI,CAAC3C,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;QAChC;MACJ,CAAC,CAAC;IACN;EAAC;AAAA;AAGL,iEAAeoC,OAAO,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClEtB;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5CmB,gBAAgB,0BAAAC,iBAAA;EAAA,SAAAD,iBAAA;IAAAzE,eAAA,OAAAyE,gBAAA;IAAA,OAAAE,UAAA,OAAAF,gBAAA,EAAA5E,SAAA;EAAA;EAAA+E,SAAA,CAAAH,gBAAA,EAAAC,iBAAA;EAAA,OAAAjE,YAAA,CAAAgE,gBAAA;IAAA/D,GAAA;IAAAC,KAAA;IAClB;AACJ;AACA;IACI,SAAAkE,MAAMA,CAAA,EAAG;MAAA,IAAAzC,KAAA;MACL,IAAI,CAAC0C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAIC,KAAK,GAAG,EAAE;MACd,IAAI;QACAA,KAAK,GAAG,OAAO,IAAI,CAACC,QAAQ,CAACD,KAAK,KAAK,QAAQ,GAC3CE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,QAAQ,CAACD,KAAK,CAAC,GAAG,IAAI,CAACC,QAAQ,CAACD,KAAK;MAC7D,CAAC,CAAC,OAAOI,CAAC,EAAE;QACRzC,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEwC,CAAC,CAAC;QACjD;MACJ;;MAEA;MACA,IAAI,CAACJ,KAAK,IAAI,CAACA,KAAK,CAACjF,MAAM,EAAE;QACzB4C,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAE,IAAI,CAACqC,QAAQ,CAACjE,EAAE,CAAC;QACxE;MACJ;;MAEA;MACA,IAAMqE,iBAAiB,GAAGxE,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACvDsE,iBAAiB,CAACpE,SAAS,GAAG,0BAA0B;;MAExD;MACA,IAAMqE,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;;MAE9C;MACA,IAAMC,UAAU,GAAG3E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAChDyE,UAAU,CAACvE,SAAS,GAAG,2CAA2C;;MAElE;MACA,IAAMwE,WAAW,GAAG5E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACjD0E,WAAW,CAACxE,SAAS,GAAG,4CAA4C;;MAEpE;MACA,IAAMyE,UAAU,GAAG7E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAChD2E,UAAU,CAACzE,SAAS,GAAG,uBAAuB;MAC9CyE,UAAU,CAAChE,WAAW,GAAG,SAAS;MAClC8D,UAAU,CAACnE,WAAW,CAACqE,UAAU,CAAC;MAElC,IAAMC,WAAW,GAAG9E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACjD4E,WAAW,CAAC1E,SAAS,GAAG,uBAAuB;MAC/C0E,WAAW,CAACjE,WAAW,GAAG,YAAY;MACtC+D,WAAW,CAACpE,WAAW,CAACsE,WAAW,CAAC;;MAEpC;MACA,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,aAAa,GAAG,IAAI;;MAEzB;MACA,IAAI,CAACC,UAAU,GAAGd,KAAK,CAACjF,MAAM;MAC9B,IAAI,CAACgG,WAAW,GAAG,CAAC;;MAEpB;MACAf,KAAK,CAACpB,OAAO,CAAC,UAACC,IAAI,EAAEmC,KAAK,EAAK;QAC3B;QACA,IAAMC,QAAQ,GAAGpF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC9CkF,QAAQ,CAAChF,SAAS,GAAG,qBAAqB;QAC1CgF,QAAQ,CAACC,OAAO,CAACC,MAAM,GAAGtC,IAAI,CAAC7C,EAAE;QAEjC,IAAMoF,aAAa,GAAGvF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACnDqF,aAAa,CAACnF,SAAS,GAAG,+BAA+B;;QAEzD;QACA,IAAMoF,QAAQ,GAAGxF,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;QAC/CsF,QAAQ,CAACpF,SAAS,GAAG,qBAAqB;QAC1CoF,QAAQ,CAAC3E,WAAW,GAAGmC,IAAI,CAACyC,MAAM;QAClCF,aAAa,CAAC/E,WAAW,CAACgF,QAAQ,CAAC;;QAEnC;QACA,IAAIxC,IAAI,CAAC0C,UAAU,EAAE;UACjB,IAAMC,WAAW,GAAG3F,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;UACpDyF,WAAW,CAACvF,SAAS,GAAG,0BAA0B;UAClDuF,WAAW,CAACC,SAAS,GAAG,osBAAosB;UAE5tBD,WAAW,CAAC1C,gBAAgB,CAAC,OAAO,EAAE,UAACsB,CAAC,EAAK;YACzCA,CAAC,CAACsB,eAAe,CAAC,CAAC,CAAC,CAAC;YACrB,IAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC/C,IAAI,CAAC0C,UAAU,CAAC;YACxCI,KAAK,CAACE,IAAI,CAAC,CAAC,SAAM,CAAC,UAAAzB,CAAC,EAAI;cACpBzC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEwC,CAAC,CAAC;YAC5C,CAAC,CAAC;UACN,CAAC,CAAC;UAEFgB,aAAa,CAAC/E,WAAW,CAACmF,WAAW,CAAC;QAC1C;QAEAP,QAAQ,CAAC5E,WAAW,CAAC+E,aAAa,CAAC;;QAEnC;QACAH,QAAQ,CAACnC,gBAAgB,CAAC,OAAO,EAAE,YAAM;UACrC,IAAIzB,KAAI,CAACyE,YAAY,EAAE;;UAEvB;UACAzE,KAAI,CAAC0E,mBAAmB,CAACd,QAAQ,CAAC;;UAElC;UACA5D,KAAI,CAAC2E,aAAa,CAAC,CAAC;QACxB,CAAC,CAAC;QAEFxB,UAAU,CAACnE,WAAW,CAAC4E,QAAQ,CAAC;;QAEhC;QACA,IAAMgB,SAAS,GAAGpG,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC/CkG,SAAS,CAAChG,SAAS,GAAG,qBAAqB;QAC3CgG,SAAS,CAACf,OAAO,CAACC,MAAM,GAAGtC,IAAI,CAAC7C,EAAE;QAClCiG,SAAS,CAACvF,WAAW,GAAGmC,IAAI,CAACqD,QAAQ;;QAErC;QACAD,SAAS,CAACnD,gBAAgB,CAAC,OAAO,EAAE,YAAM;UACtC,IAAIzB,KAAI,CAACyE,YAAY,EAAE;UAEvBnE,OAAO,CAACwE,GAAG,CAAC,oBAAoB,CAAC;;UAEjC;UACA9E,KAAI,CAAC+E,oBAAoB,CAACH,SAAS,CAAC;;UAEpC;UACA5E,KAAI,CAAC2E,aAAa,CAAC,CAAC;QACxB,CAAC,CAAC;QAEFvB,WAAW,CAACpE,WAAW,CAAC4F,SAAS,CAAC;MACtC,CAAC,CAAC;;MAEF;MACA5B,iBAAiB,CAAChE,WAAW,CAACmE,UAAU,CAAC;MACzCH,iBAAiB,CAAChE,WAAW,CAACoE,WAAW,CAAC;;MAE1C;MACA,IAAI,CAACnF,SAAS,CAACe,WAAW,CAACgE,iBAAiB,CAAC;;MAE7C;MACA,IAAMgC,YAAY,GAAGxG,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;MACrDsG,YAAY,CAACpG,SAAS,GAAG,kBAAkB;MAC3CoG,YAAY,CAAC3F,WAAW,GAAG,gBAAgB;MAC3C2F,YAAY,CAACC,QAAQ,GAAG,IAAI;MAC5BD,YAAY,CAACvD,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACzC;QACA,IAAI,OAAOzB,KAAI,CAACxC,OAAO,CAAC0H,QAAQ,KAAK,UAAU,EAAE;UAC7ClF,KAAI,CAACxC,OAAO,CAAC0H,QAAQ,CAAClF,KAAI,CAAC4C,QAAQ,CAACjE,EAAE,CAAC;QAC3C;MACJ,CAAC,CAAC;MACF,IAAI,CAACV,SAAS,CAACe,WAAW,CAACgG,YAAY,CAAC;MACxC,IAAI,CAACA,YAAY,GAAGA,YAAY;;MAEhC;MACA,IAAI,CAACG,SAAS,CAAC,CAAC;;MAEhB;MACA,IAAI,IAAI,CAACV,YAAY,EAAE;QACnB,IAAI,CAACW,kBAAkB,CAACzC,KAAK,EAAEM,WAAW,CAAC;MAC/C,CAAC,MAAM;QACH;QACA,IAAI,CAACoC,mBAAmB,CAAC1C,KAAK,EAAEM,WAAW,CAAC;MAChD;IACJ;;IAEA;AACJ;AACA;EAFI;IAAA3E,GAAA;IAAAC,KAAA,EAGA,SAAA4G,SAASA,CAAA,EAAG;MACR;MACA,IAAI3G,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC,EAAE;MAEtD,IAAM6G,OAAO,GAAG9G,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAC/C4G,OAAO,CAAC3G,EAAE,GAAG,uBAAuB;MACpC2G,OAAO,CAACjG,WAAW,kmIA+HlB;MAEDb,QAAQ,CAAC+G,IAAI,CAACvG,WAAW,CAACsG,OAAO,CAAC;IACtC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhH,GAAA;IAAAC,KAAA,EAKA,SAAAmG,mBAAmBA,CAAClD,IAAI,EAAE;MACtB;MACA,IAAIA,IAAI,CAAC3C,SAAS,CAAC2G,QAAQ,CAAC,QAAQ,CAAC,IAAIhE,IAAI,CAAC3C,SAAS,CAAC2G,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC1E;MACJ;;MAEA;MACA,IAAI,IAAI,CAACjC,YAAY,KAAK/B,IAAI,EAAE;QAC5BA,IAAI,CAAC3C,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC0B,YAAY,GAAG,IAAI;QACxB;MACJ;;MAEA;MACA,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC1E,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;MAClD;;MAEA;MACAL,IAAI,CAAC3C,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC9B,IAAI,CAACyE,YAAY,GAAG/B,IAAI;IAC5B;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAlD,GAAA;IAAAC,KAAA,EAKA,SAAAwG,oBAAoBA,CAACvD,IAAI,EAAE;MACvB;MACA,IAAIA,IAAI,CAAC3C,SAAS,CAAC2G,QAAQ,CAAC,QAAQ,CAAC,IAAIhE,IAAI,CAAC3C,SAAS,CAAC2G,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC1E;MACJ;;MAEA;MACA,IAAI,IAAI,CAAChC,aAAa,KAAKhC,IAAI,EAAE;QAC7BA,IAAI,CAAC3C,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC2B,aAAa,GAAG,IAAI;QACzB;MACJ;;MAEA;MACA,IAAI,IAAI,CAACA,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC3E,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;MACnD;;MAEA;MACAL,IAAI,CAAC3C,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC9B,IAAI,CAAC0E,aAAa,GAAGhC,IAAI;IAC7B;;IAEA;AACJ;AACA;EAFI;IAAAlD,GAAA;IAAAC,KAAA,EAGA,SAAAoG,aAAaA,CAAA,EAAG;MACZrE,OAAO,CAACwE,GAAG,CAAC,uBAAuB,CAAC;MACpC,IAAI,CAAC,IAAI,CAACvB,YAAY,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MAE/C,IAAMiC,MAAM,GAAG,IAAI,CAAClC,YAAY,CAACM,OAAO,CAACC,MAAM;MAC/C,IAAM4B,OAAO,GAAG,IAAI,CAAClC,aAAa,CAACK,OAAO,CAACC,MAAM;;MAEjD;MACA,IAAM6B,MAAM,GAAG,IAAI,CAACzC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;MACzCyC,MAAM,CAACF,MAAM,CAAC,GAAGC,OAAO;MACxB,IAAI,CAACE,aAAa,CAACD,MAAM,CAAC;;MAE1B;MACA,IAAI,CAACpC,YAAY,CAAC1E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC3C,IAAI,CAAC0E,aAAa,CAAC3E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE5C;MACA,IAAI,CAACyE,YAAY,CAAC1E,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MACzC,IAAI,CAAC0E,aAAa,CAAC3E,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC1C,IAAI,CAACyE,YAAY,CAAC1E,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;MAC9C,IAAI,CAAC2B,aAAa,CAAC3E,SAAS,CAACgD,MAAM,CAAC,UAAU,CAAC;;MAE/C;MACA,IAAI,CAAC0B,YAAY,CAAC1E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC3C,IAAI,CAAC0E,aAAa,CAAC3E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE5C;MACA,IAAI,CAACyE,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,aAAa,GAAG,IAAI;;MAEzB;MACA,IAAI,CAACqC,cAAc,CAAC,CAAC;IACzB;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAvH,GAAA;IAAAC,KAAA,EAKA,SAAAqH,aAAaA,CAACD,MAAM,EAAE;MAClB,IAAI,CAACG,UAAU,GAAGH,MAAM;IAC5B;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAArH,GAAA;IAAAC,KAAA,EAMA,SAAA6G,kBAAkBA,CAACzC,KAAK,EAAEM,WAAW,EAAE;MACnC,IAAM8C,SAAS,GAAG,IAAI,CAAC9H,SAAS,CAACmD,gBAAgB,CAAC,2CAA2C,CAAC;MAC9F,IAAM4E,UAAU,GAAG,IAAI,CAAC/H,SAAS,CAACmD,gBAAgB,CAAC,4CAA4C,CAAC;MAEhGuB,KAAK,CAACpB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAClB,IAAMyE,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACxC,OAAO,CAACC,MAAM,KAAKtC,IAAI,CAAC7C,EAAE;QAAA,EAAC;QACpF,IAAM2H,SAAS,GAAGJ,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACxC,OAAO,CAACC,MAAM,KAAKtC,IAAI,CAAC7C,EAAE;QAAA,EAAC;QAEtF,IAAIsH,QAAQ,IAAIK,SAAS,EAAE;UACvB,IAAMR,UAAU,GAAG7C,WAAW,CAACzB,IAAI,CAAC7C,EAAE,CAAC;UAEvC,IAAImH,UAAU,KAAKtE,IAAI,CAAC7C,EAAE,EAAE;YACxB;YACAsH,QAAQ,CAACpH,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;YACjCwH,SAAS,CAACzH,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;UACtC,CAAC,MAAM,IAAIgH,UAAU,EAAE;YACnB;YACAG,QAAQ,CAACpH,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;YAEnC;YACA,IAAMyH,cAAc,GAAGL,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;cAAA,OAAIA,IAAI,CAACxC,OAAO,CAACC,MAAM,KAAKgC,UAAU;YAAA,EAAC;YAC9F,IAAIS,cAAc,EAAE;cAChBA,cAAc,CAAC1H,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;YAC7C;UACJ;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAR,GAAA;IAAAC,KAAA,EAMA,SAAA8G,mBAAmBA,CAAC1C,KAAK,EAAEM,WAAW,EAAE;MACpC,IAAM8C,SAAS,GAAG,IAAI,CAAC9H,SAAS,CAACmD,gBAAgB,CAAC,2CAA2C,CAAC;MAC9F,IAAM4E,UAAU,GAAG,IAAI,CAAC/H,SAAS,CAACmD,gBAAgB,CAAC,4CAA4C,CAAC;MAEhG,IAAIsC,WAAW,GAAG,CAAC;MAEnBf,KAAK,CAACpB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAClB,IAAMyE,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACxC,OAAO,CAACC,MAAM,KAAKtC,IAAI,CAAC7C,EAAE;QAAA,EAAC;QACpF,IAAMmH,UAAU,GAAG7C,WAAW,CAACzB,IAAI,CAAC7C,EAAE,CAAC;QAEvC,IAAIsH,QAAQ,IAAIH,UAAU,EAAE;UACxB,IAAMQ,SAAS,GAAGJ,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;YAAA,OAAIA,IAAI,CAACxC,OAAO,CAACC,MAAM,KAAKgC,UAAU;UAAA,EAAC;UAEzF,IAAIQ,SAAS,EAAE;YACX;YACAL,QAAQ,CAACpH,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;YAChCwH,SAAS,CAACzH,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;YACjCmH,QAAQ,CAACpH,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;YAClCwH,SAAS,CAACzH,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;YACnC4E,WAAW,EAAE;UACjB;QACJ;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI,CAACA,WAAW,GAAGA,WAAW;;MAE9B;MACA,IAAI,CAACmC,cAAc,CAAC,CAAC;IACzB;;IAEA;AACJ;AACA;EAFI;IAAAvH,GAAA;IAAAC,KAAA,EAGA,SAAAsH,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACD,UAAU,EAAE;QACrC;QACA,IAAI,IAAI,CAACuB,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACnG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UACzC,IAAI,CAACkG,YAAY,CAACC,QAAQ,GAAG,KAAK;QACtC;MACJ,CAAC,MAAM;QACH;QACA,IAAI,IAAI,CAACD,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACC,QAAQ,GAAG,IAAI;QACrC;MACJ;;MAEA;MACA,IAAI,CAACvB,WAAW,GAAG,IAAI,CAACzF,SAAS,CAACmD,gBAAgB,CAAC,6BAA6B,CAAC,CAAC1D,MAAM,GAAG,CAAC;IAChG;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAY,GAAA;IAAAC,KAAA,EAMA,SAAAiI,gBAAgBA,CAAC7D,KAAK,EAAE;MACpB;MACA,IAAM8D,SAAS,GAAG9D,KAAK,CAAC+D,GAAG,CAAC,UAAAlF,IAAI;QAAA,OAAK;UACjC7C,EAAE,EAAE6C,IAAI,CAAC7C,EAAE;UACXkG,QAAQ,EAAErD,IAAI,CAACqD;QACnB,CAAC;MAAA,CAAC,CAAC;;MAEH;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;QACpB,KAAK,IAAIkC,CAAC,GAAGF,SAAS,CAAC/I,MAAM,GAAG,CAAC,EAAEiJ,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,IAAMC,CAAC,GAAGnG,IAAI,CAACoG,KAAK,CAACpG,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIiG,CAAC,GAAG,CAAC,CAAC,CAAC;UAAC,IAAAG,IAAA,GACf,CAACL,SAAS,CAACG,CAAC,CAAC,EAAEH,SAAS,CAACE,CAAC,CAAC,CAAC;UAA1DF,SAAS,CAACE,CAAC,CAAC,GAAAG,IAAA;UAAEL,SAAS,CAACG,CAAC,CAAC,GAAAE,IAAA;QAC/B;MACJ;MAEA,OAAOL,SAAS;IACpB;EAAC;AAAA,EAvgB0BrE,yDAAgB;AA0gB/C,iEAAeC,gBAAgB,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClhB/B;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5C0E,sBAAsB,0BAAAzE,iBAAA;EAAA,SAAAyE,uBAAA;IAAAnJ,eAAA,OAAAmJ,sBAAA;IAAA,OAAAxE,UAAA,OAAAwE,sBAAA,EAAAtJ,SAAA;EAAA;EAAA+E,SAAA,CAAAuE,sBAAA,EAAAzE,iBAAA;EAAA,OAAAjE,YAAA,CAAA0I,sBAAA;IAAAzI,GAAA;IAAAC,KAAA;IACxB;AACJ;AACA;IACI,SAAAkE,MAAMA,CAAA,EAAG;MAAA,IAAAzC,KAAA;MACL,IAAI,CAAC0C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAIlF,OAAO,GAAG,EAAE;MAChB,IAAI;QACAA,OAAO,GAAG,OAAO,IAAI,CAACoF,QAAQ,CAACpF,OAAO,KAAK,QAAQ,GAC/CqF,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,QAAQ,CAACpF,OAAO,CAAC,GAAG,IAAI,CAACoF,QAAQ,CAACpF,OAAO;MACjE,CAAC,CAAC,OAAOuF,CAAC,EAAE;QACRzC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEwC,CAAC,CAAC;QACnD;MACJ;;MAEA;MACA,IAAI,CAACvF,OAAO,IAAI,CAACA,OAAO,CAACE,MAAM,EAAE;QAC7B4C,OAAO,CAACC,KAAK,CAAC,gDAAgD,EAAE,IAAI,CAACqC,QAAQ,CAACjE,EAAE,CAAC;QACjF;MACJ;;MAEA;MACAnB,OAAO,CAAC+D,OAAO,CAAC,UAACyF,MAAM,EAAErD,KAAK,EAAK;QAC/B,IAAMsD,aAAa,GAAGjH,KAAI,CAACkH,mBAAmB,CAACF,MAAM,EAAErD,KAAK,CAAC;QAC7D3D,KAAI,CAAC/B,SAAS,CAACe,WAAW,CAACiI,aAAa,CAAC;MAC7C,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA3I,GAAA;IAAAC,KAAA,EAOA,SAAA2I,mBAAmBA,CAACF,MAAM,EAAErD,KAAK,EAAE;MAAA,IAAAwD,MAAA;MAC/B,IAAMF,aAAa,GAAGzI,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACnDuI,aAAa,CAACrI,SAAS,GAAG,uBAAuB;MAEjD,IAAMwI,OAAO,eAAAlI,MAAA,CAAe,IAAI,CAAC0D,QAAQ,CAACjE,EAAE,cAAAO,MAAA,CAAWyE,KAAK,CAAE;MAC9D,IAAM0D,SAAS,GAAG,IAAI,CAACnE,aAAa,CAAC,CAAC,KAAK8D,MAAM,CAACzI,KAAK;MAEvD0I,aAAa,CAAC7C,SAAS,oEAAAlF,MAAA,CAENkI,OAAO,8CAAAlI,MAAA,CACI,IAAI,CAAC0D,QAAQ,CAACjE,EAAE,sCAAAO,MAAA,CACxB8H,MAAM,CAACzI,KAAK,8BAAAW,MAAA,CACnBmI,SAAS,GAAG,SAAS,GAAG,EAAE,2BAAAnI,MAAA,CAC1B,IAAI,CAACuF,YAAY,GAAG,UAAU,GAAG,EAAE,gDAAAvF,MAAA,CAE9BkI,OAAO,SAAAlI,MAAA,CAAK8H,MAAM,CAAClJ,IAAI,uBACxC;;MAED;MACA,IAAI,IAAI,CAAC2G,YAAY,IAAI,IAAI,CAAC7B,QAAQ,CAAC0E,cAAc,EAAE;QACnD,IAAIN,MAAM,CAACzI,KAAK,KAAK,IAAI,CAACqE,QAAQ,CAAC0E,cAAc,EAAE;UAC/CL,aAAa,CAACpI,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QAC1C,CAAC,MAAM,IAAIuI,SAAS,EAAE;UAClBJ,aAAa,CAACpI,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;QAC5C;MACJ;;MAEA;MACA,IAAI,CAAC,IAAI,CAAC2F,YAAY,EAAE;QACpB,IAAM8C,KAAK,GAAGN,aAAa,CAACtH,aAAa,CAAC,OAAO,CAAC;QAClD,IAAI4H,KAAK,IAAI,OAAO,IAAI,CAAC/J,OAAO,CAACgK,gBAAgB,KAAK,UAAU,EAAE;UAC9DD,KAAK,CAAC9F,gBAAgB,CAAC,QAAQ,EAAE,YAAM;YACnC0F,MAAI,CAAC3J,OAAO,CAACgK,gBAAgB,CAACL,MAAI,CAACvE,QAAQ,CAACjE,EAAE,EAAEqI,MAAM,CAACzI,KAAK,CAAC;UACjE,CAAC,CAAC;QACN;MACJ;MAEA,OAAO0I,aAAa;IACxB;EAAC;AAAA,EA3EgC7E,yDAAgB;AA8ErD,iEAAe2E,sBAAsB,E;;;;;;;;;;;;;;;;;;;;ACtFrC;AACA;AACA;AACA;AACA;AACA;AALA,IAOM3E,gBAAgB;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,iBAAYQ,QAAQ,EAAE3E,SAAS,EAAgB;IAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAwE,gBAAA;IACzC,IAAI,iBAAAA,gBAAA,QAAAqF,WAAA,eAAerF,gBAAgB,EAAE;MACjC,MAAM,IAAIsF,KAAK,CAAC,2EAA2E,CAAC;IAChG;IAEA,IAAI,CAAC9E,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3E,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiH,YAAY,GAAGjH,OAAO,CAACiH,YAAY,IAAI,KAAK;IACjD,IAAI,CAACxB,WAAW,GAAGzF,OAAO,CAACyF,WAAW,IAAI,CAAC,CAAC;EAChD;;EAEA;AACJ;AACA;AACA;EAHI,OAAA5E,YAAA,CAAA+D,gBAAA;IAAA9D,GAAA;IAAAC,KAAA,EAIA,SAAAkE,MAAMA,CAAA,EAAG;MACL,MAAM,IAAIiF,KAAK,CAAC,iDAAiD,CAAC;IACtE;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAApJ,GAAA;IAAAC,KAAA,EAKA,SAAA2E,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACD,WAAW,CAAC,IAAI,CAACL,QAAQ,CAACjE,EAAE,CAAC,IAAI,IAAI;IACrD;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAL,GAAA;IAAAC,KAAA,EAKA,SAAAoJ,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC/E,QAAQ,CAACjE,EAAE,IAAI,IAAI,CAACsE,WAAW;IAC/C;;IAEA;AACJ;AACA;EAFI;IAAA3E,GAAA;IAAAC,KAAA,EAGA,SAAAmE,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACzE,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACmG,SAAS,GAAG,EAAE;MACjC;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA9F,GAAA;IAAAC,KAAA,EAKA,SAAAqJ,aAAaA,CAAA,EAAG;MACZ,IAAMC,OAAO,GAAGrJ,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC7CmJ,OAAO,CAACjJ,SAAS,GAAG,wBAAwB;MAC5CiJ,OAAO,CAAChE,OAAO,CAACiE,UAAU,GAAG,IAAI,CAAClF,QAAQ,CAACjE,EAAE;MAC7CkJ,OAAO,CAAChE,OAAO,CAACkE,YAAY,GAAG,IAAI,CAACnF,QAAQ,CAACoF,IAAI;MACjD,OAAOH,OAAO;IAClB;EAAC;AAAA;AAGL,iEAAezF,gBAAgB,E;;;;;;;;;;;;;;;;;;;;;;;AC5E/B;AACA;AACA;AACA;AACA;;AAE8D;AACV;AACF;AAAA,IAE5C8F,uBAAuB;EAAA,SAAAA,wBAAA;IAAAtK,eAAA,OAAAsK,uBAAA;EAAA;EAAA,OAAA7J,YAAA,CAAA6J,uBAAA;IAAA5J,GAAA;IAAAC,KAAA;IACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAAO4J,cAAcA,CAACvF,QAAQ,EAAE3E,SAAS,EAAgB;MAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnD,IAAI,CAACmF,QAAQ,IAAI,CAACA,QAAQ,CAACwF,aAAa,EAAE;QACtC,MAAM,IAAIV,KAAK,CAAC,4CAA4C,CAAC;MACjE;;MAEA;MACA,QAAQ9E,QAAQ,CAACwF,aAAa,CAACC,WAAW,CAAC,CAAC;QACxC,KAAK,iBAAiB;UAClB,OAAO,IAAItB,+DAAsB,CAACnE,QAAQ,EAAE3E,SAAS,EAAET,OAAO,CAAC;QAEnE,KAAK,YAAY;QACjB,KAAK,cAAc;QACnB,KAAK,eAAe;UAChB,OAAO,IAAIyK,0DAAiB,CAACrF,QAAQ,EAAE3E,SAAS,EAAET,OAAO,CAAC;QAE9D,KAAK,UAAU;UACX,OAAO,IAAI6E,yDAAgB,CAACO,QAAQ,EAAE3E,SAAS,EAAET,OAAO,CAAC;QAE7D;UACI,MAAM,IAAIkK,KAAK,6CAAAxI,MAAA,CAA6C0D,QAAQ,CAACwF,aAAa,CAAE,CAAC;MAC7F;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA9J,GAAA;IAAAC,KAAA,EAMA,SAAO+J,kBAAkBA,CAACP,YAAY,EAAE;MACpC,IAAI,CAACA,YAAY,EAAE,OAAO,KAAK;MAE/B,IAAMQ,cAAc,GAAG,CACnB,iBAAiB,EACjB,YAAY,EACZ,cAAc,EACd,eAAe,EACf,UAAU,CACb;MAED,OAAOA,cAAc,CAACC,QAAQ,CAACT,YAAY,CAACM,WAAW,CAAC,CAAC,CAAC;IAC9D;EAAC;AAAA;AAGL,iEAAeH,uBAAuB,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChEtC;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5CD,iBAAiB,0BAAA3F,iBAAA;EAAA,SAAA2F,kBAAA;IAAArK,eAAA,OAAAqK,iBAAA;IAAA,OAAA1F,UAAA,OAAA0F,iBAAA,EAAAxK,SAAA;EAAA;EAAA+E,SAAA,CAAAyF,iBAAA,EAAA3F,iBAAA;EAAA,OAAAjE,YAAA,CAAA4J,iBAAA;IAAA3J,GAAA;IAAAC,KAAA;IACnB;AACJ;AACA;IACI,SAAAkE,MAAMA,CAAA,EAAG;MAAA,IAAAzC,KAAA;MACL,IAAI,CAAC0C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAMmF,OAAO,GAAG,IAAI,CAACD,aAAa,CAAC,CAAC;;MAEpC;MACA,IAAMa,cAAc,GAAGjK,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACpD+J,cAAc,CAAC7J,SAAS,GAAG,gCAAgC;MAE3D,IAAMwI,OAAO,eAAAlI,MAAA,CAAe,IAAI,CAAC0D,QAAQ,CAACjE,EAAE,WAAQ;MACpD,IAAMmH,UAAU,GAAG,IAAI,CAAC5C,aAAa,CAAC,CAAC,IAAI,EAAE;MAE7CuF,cAAc,CAACrE,SAAS,mEAAAlF,MAAA,CAEPkI,OAAO,8FAAAlI,MAAA,CAEJ4G,UAAU,2CAAA5G,MAAA,CACJ,IAAI,CAAC0D,QAAQ,CAAC8F,WAAW,IAAI,0BAA0B,6BAAAxJ,MAAA,CACpE,IAAI,CAACuF,YAAY,GAAG,UAAU,GAAG,EAAE,8BAE/C;;MAED;MACA,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC7B,QAAQ,CAAC0E,cAAc,EAAE;QACnD,IAAMqB,UAAU,GAAGnK,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAChDiK,UAAU,CAAC/J,SAAS,GAAG,4BAA4B;QAEnD,IAAMgK,SAAS,GAAG9C,UAAU,CAACuC,WAAW,CAAC,CAAC,KAAK,IAAI,CAACzF,QAAQ,CAAC0E,cAAc,CAACe,WAAW,CAAC,CAAC;QACzFM,UAAU,CAAC9J,SAAS,CAACC,GAAG,CAAC8J,SAAS,GAAG,SAAS,GAAG,WAAW,CAAC;QAE7DD,UAAU,CAACvE,SAAS,4HAAAlF,MAAA,CAEuB,IAAI,CAAC0D,QAAQ,CAAC0E,cAAc,2CAEtE;QAEDmB,cAAc,CAACzJ,WAAW,CAAC2J,UAAU,CAAC;MAC1C;MAEAd,OAAO,CAAC7I,WAAW,CAACyJ,cAAc,CAAC;MACnC,IAAI,CAACxK,SAAS,CAACe,WAAW,CAAC6I,OAAO,CAAC;;MAEnC;MACA,IAAI,CAAC,IAAI,CAACpD,YAAY,EAAE;QACpB,IAAM8C,KAAK,GAAGM,OAAO,CAAClI,aAAa,CAAC,OAAO,CAAC;QAC5C,IAAI4H,KAAK,IAAI,OAAO,IAAI,CAAC/J,OAAO,CAACgK,gBAAgB,KAAK,UAAU,EAAE;UAC9DD,KAAK,CAAC9F,gBAAgB,CAAC,QAAQ,EAAE,UAACsB,CAAC,EAAK;YACpC/C,KAAI,CAACxC,OAAO,CAACgK,gBAAgB,CAACxH,KAAI,CAAC4C,QAAQ,CAACjE,EAAE,EAAEoE,CAAC,CAAC8F,MAAM,CAACtK,KAAK,CAAC;UACnE,CAAC,CAAC;;UAEF;UACAgJ,KAAK,CAAC9F,gBAAgB,CAAC,OAAO,EAAE,UAACsB,CAAC,EAAK;YACnC/C,KAAI,CAACxC,OAAO,CAACgK,gBAAgB,CAACxH,KAAI,CAAC4C,QAAQ,CAACjE,EAAE,EAAEoE,CAAC,CAAC8F,MAAM,CAACtK,KAAK,CAAC;UACnE,CAAC,CAAC;QACN;MACJ;IACJ;EAAC;AAAA,EA7D2B6D,yDAAgB;AAgEhD,iEAAe6F,iBAAiB,E;;;;;;;;;;;;;;;;;;;;;;;ACxEhC;AACA;AACA;AACA;AACA;;AAEkD;AACY;AACV;AACF;AACc;;;;;;;;;;;;;;;;;0BCThE,uKAAAlF,CAAA,EAAA+F,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAzC,EAAAoC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAxC,CAAA,QAAA0C,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAxC,CAAA,EAAA0C,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAW,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAZ,CAAA,KAAAe,CAAA,EAAAjH,CAAA,EAAAkH,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAvI,IAAA,CAAAoB,CAAA,MAAAmH,CAAA,WAAAA,EAAApB,CAAA,EAAAC,CAAA,WAAApC,CAAA,GAAAmC,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAAzG,CAAA,EAAAgH,CAAA,CAAAd,CAAA,GAAAF,CAAA,EAAAkB,CAAA,gBAAAC,EAAAnB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAgB,CAAA,IAAAF,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAAnM,MAAA,EAAAoL,CAAA,UAAAK,CAAA,EAAAxC,CAAA,GAAAkD,CAAA,CAAAf,CAAA,GAAAoB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAM,CAAA,GAAAxD,CAAA,KAAAoC,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAA7C,CAAA,EAAA0C,CAAA,GAAA1C,CAAA,YAAA0C,CAAA,WAAA1C,CAAA,MAAAA,CAAA,MAAA5D,CAAA,IAAA4D,CAAA,OAAAuD,CAAA,MAAAf,CAAA,GAAAJ,CAAA,QAAAmB,CAAA,GAAAvD,CAAA,QAAA0C,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAf,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAtC,CAAA,OAAAuD,CAAA,GAAAC,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAApC,CAAA,MAAAsC,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAAxD,CAAA,MAAAoC,CAAA,EAAApC,CAAA,MAAAsC,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAkB,CAAA,QAAAH,CAAA,OAAAb,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAN,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAAtG,CAAA,GAAAyG,CAAA,MAAAM,CAAA,KAAAnD,CAAA,KAAA0C,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAd,CAAA,QAAAiB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAd,CAAA,GAAAO,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAjD,CAAA,QAAA0C,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAnC,CAAA,CAAAwC,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,EAAA6C,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAAvK,KAAA,EAAA8K,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAnC,CAAA,eAAAmC,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,GAAA0C,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAA1C,CAAA,GAAA5D,CAAA,cAAA+F,CAAA,IAAAgB,CAAA,GAAAC,CAAA,CAAAd,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAc,CAAA,OAAAE,CAAA,kBAAAnB,CAAA,IAAAnC,CAAA,GAAA5D,CAAA,EAAAsG,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAArL,KAAA,EAAAuK,CAAA,EAAAwB,IAAA,EAAAR,CAAA,SAAAf,CAAA,EAAAI,CAAA,EAAAxC,CAAA,QAAA6C,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAgB,kBAAA,cAAAC,2BAAA,KAAA1B,CAAA,GAAAW,MAAA,CAAAgB,cAAA,MAAApB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAgB,0BAAA,CAAAlB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAA7G,CAAA,WAAA0G,MAAA,CAAAiB,cAAA,GAAAjB,MAAA,CAAAiB,cAAA,CAAA3H,CAAA,EAAAyH,0BAAA,KAAAzH,CAAA,CAAA4H,SAAA,GAAAH,0BAAA,EAAAb,mBAAA,CAAA5G,CAAA,EAAAoG,CAAA,yBAAApG,CAAA,CAAAuG,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAzG,CAAA,WAAAwH,iBAAA,CAAAjB,SAAA,GAAAkB,0BAAA,EAAAb,mBAAA,CAAAH,CAAA,iBAAAgB,0BAAA,GAAAb,mBAAA,CAAAa,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAjB,mBAAA,CAAAa,0BAAA,EAAArB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAqB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAnE,CAAA,EAAAoE,CAAA,EAAAnB,CAAA;AAAA,SAAAD,oBAAA5G,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAnC,CAAA,GAAA8C,MAAA,CAAAuB,cAAA,QAAArE,CAAA,uBAAA5D,CAAA,IAAA4D,CAAA,QAAAgD,mBAAA,YAAAsB,mBAAAlI,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAApC,CAAA,GAAAA,CAAA,CAAA5D,CAAA,EAAAgG,CAAA,IAAAxK,KAAA,EAAA0K,CAAA,EAAAiC,UAAA,GAAApC,CAAA,EAAAqC,YAAA,GAAArC,CAAA,EAAAsC,QAAA,GAAAtC,CAAA,MAAA/F,CAAA,CAAAgG,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAA5G,CAAA,EAAAgG,CAAA,YAAAhG,CAAA,gBAAAsI,OAAA,CAAAtC,CAAA,EAAAE,CAAA,EAAAlG,CAAA,UAAAoG,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAA5G,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAwC,mBAAArC,CAAA,EAAAH,CAAA,EAAA/F,CAAA,EAAAgG,CAAA,EAAAI,CAAA,EAAAc,CAAA,EAAAZ,CAAA,cAAA1C,CAAA,GAAAsC,CAAA,CAAAgB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAA7C,CAAA,CAAApI,KAAA,WAAA0K,CAAA,gBAAAlG,CAAA,CAAAkG,CAAA,KAAAtC,CAAA,CAAA2D,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAAvJ,OAAA,CAAAC,OAAA,CAAAsJ,CAAA,EAAA+B,IAAA,CAAAxC,CAAA,EAAAI,CAAA;AAAA,SAAAqC,kBAAAvC,CAAA,6BAAAH,CAAA,SAAA/F,CAAA,GAAAtF,SAAA,aAAAwC,OAAA,WAAA8I,CAAA,EAAAI,CAAA,QAAAc,CAAA,GAAAhB,CAAA,CAAAwC,KAAA,CAAA3C,CAAA,EAAA/F,CAAA,YAAA2I,MAAAzC,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,UAAA1C,CAAA,cAAA0C,OAAA1C,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,WAAA1C,CAAA,KAAAyC,KAAA;AAAA,SAAA9N,gBAAAqM,CAAA,EAAAhB,CAAA,UAAAgB,CAAA,YAAAhB,CAAA,aAAAmB,SAAA;AAAA,SAAAwB,kBAAA7I,CAAA,EAAAgG,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAArL,MAAA,EAAAoL,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAA+B,UAAA,GAAA/B,CAAA,CAAA+B,UAAA,QAAA/B,CAAA,CAAAgC,YAAA,kBAAAhC,CAAA,KAAAA,CAAA,CAAAiC,QAAA,QAAA3B,MAAA,CAAAuB,cAAA,CAAAjI,CAAA,EAAA8I,cAAA,CAAA1C,CAAA,CAAA7K,GAAA,GAAA6K,CAAA;AAAA,SAAA9K,aAAA0E,CAAA,EAAAgG,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA6C,iBAAA,CAAA7I,CAAA,CAAAuG,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAA8C,iBAAA,CAAA7I,CAAA,EAAA+F,CAAA,GAAAW,MAAA,CAAAuB,cAAA,CAAAjI,CAAA,iBAAAqI,QAAA,SAAArI,CAAA;AAAA,SAAA8I,eAAA/C,CAAA,QAAAnC,CAAA,GAAAmF,YAAA,CAAAhD,CAAA,gCAAAiD,OAAA,CAAApF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAmF,aAAAhD,CAAA,EAAAC,CAAA,oBAAAgD,OAAA,CAAAjD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAA/F,CAAA,GAAA+F,CAAA,CAAAE,MAAA,CAAAgD,WAAA,kBAAAjJ,CAAA,QAAA4D,CAAA,GAAA5D,CAAA,CAAAsH,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAAgD,OAAA,CAAApF,CAAA,UAAAA,CAAA,YAAAyD,SAAA,yEAAArB,CAAA,GAAAkD,MAAA,GAAAC,MAAA,EAAApD,CAAA;AADA;;AAEoE;AAAA,IAE9DsD,qBAAqB;EACvB,SAAAA,sBAAAtF,IAAA,EAA6B;IAAA,IAAhBuF,aAAa,GAAAvF,IAAA,CAAbuF,aAAa;IAAAzO,eAAA,OAAAwO,qBAAA;IACtB,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACL,aAAa,GAAGA,aAAa;;IAElC;IACA,IAAI,CAACM,oBAAoB,GAAG,IAAIR,sEAAoB,CAAC,CAAC;;IAEtD;IACA,IAAI,CAACS,SAAS,GAAGpO,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;IACtD,IAAI,CAACoO,eAAe,GAAGrO,QAAQ,CAACmB,aAAa,CAAC,mBAAmB,CAAC;IAClE,IAAI,CAACmN,iBAAiB,GAAGtO,QAAQ,CAACmB,aAAa,CAAC,qBAAqB,CAAC;IACtE,IAAI,CAACoN,YAAY,GAAGvO,QAAQ,CAACmB,aAAa,CAAC,0BAA0B,CAAC;IACtE,IAAI,CAACqN,cAAc,GAAGxO,QAAQ,CAAC4C,gBAAgB,CAAC,iBAAiB,CAAC;IAElE,IAAI,CAAChD,IAAI,CAAC,CAAC;EACf;EAAC,OAAAC,YAAA,CAAA+N,qBAAA;IAAA9N,GAAA;IAAAC,KAAA,EAED,SAAAH,IAAIA,CAAA,EAAG;MAAA,IAAA4B,KAAA;MACH;MACA,IAAI,CAAC,IAAI,CAAC4M,SAAS,EAAE;QACjBtM,OAAO,CAAC2M,IAAI,CAAC,yBAAyB,CAAC;QACvC;MACJ;MAEA3M,OAAO,CAACwE,GAAG,CAAC,qBAAqB,CAAC;;MAElC;MACA,IAAI,CAACoI,qBAAqB,CAAC,CAAC;;MAE5B;MACA,IAAI,CAACN,SAAS,CAACnL,gBAAgB,CAAC,OAAO,EAAE;QAAA,OAAMzB,KAAI,CAACmN,eAAe,CAAC,CAAC;MAAA,EAAC;MACtE,IAAI,CAACH,cAAc,CAACzL,OAAO,CAAC,UAAA6L,MAAM;QAAA,OAAIA,MAAM,CAAC3L,gBAAgB,CAAC,OAAO,EAAE,UAACsB,CAAC;UAAA,OAAK/C,KAAI,CAACqN,gBAAgB,CAACtK,CAAC,CAAC;QAAA,EAAC;MAAA,EAAC;;MAExG;MACA,IAAI,CAACuK,0BAA0B,CAAC,CAAC;IACrC;EAAC;IAAAhP,GAAA;IAAAC,KAAA;MAAA,IAAAgP,2BAAA,GAAA/B,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAyC,QAAA;QAAA,IAAAC,MAAA,EAAAC,EAAA;QAAA,OAAA7C,YAAA,GAAAC,CAAA,WAAA6C,QAAA;UAAA,kBAAAA,QAAA,CAAA9D,CAAA,GAAA8D,QAAA,CAAA1E,CAAA;YAAA;cAAA0E,QAAA,CAAA9D,CAAA;cAAA8D,QAAA,CAAA1E,CAAA;cAAA,OAE6B2E,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;gBAAExJ,KAAK,EAAE;cAAK,CAAC,CAAC;YAAA;cAAnEmJ,MAAM,GAAAE,QAAA,CAAA3D,CAAA;cACZyD,MAAM,CAACM,SAAS,CAAC,CAAC,CAACxM,OAAO,CAAC,UAAAyM,KAAK;gBAAA,OAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;cAAA,EAAC;cACjD3N,OAAO,CAACwE,GAAG,CAAC,2BAA2B,CAAC;cAAC6I,QAAA,CAAA1E,CAAA;cAAA;YAAA;cAAA0E,QAAA,CAAA9D,CAAA;cAAA6D,EAAA,GAAAC,QAAA,CAAA3D,CAAA;cAEzC1J,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAAmN,EAAO,CAAC;cACjD,IAAI,CAACQ,SAAS,CAAC,mFAAmF,CAAC;YAAC;cAAA,OAAAP,QAAA,CAAA1D,CAAA;UAAA;QAAA,GAAAuD,OAAA;MAAA,CAE3G;MAAA,SATKF,0BAA0BA,CAAA;QAAA,OAAAC,2BAAA,CAAA9B,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAA1B6P,0BAA0B;IAAA;EAAA;IAAAhP,GAAA;IAAAC,KAAA;MAAA,IAAA4P,gBAAA,GAAA3C,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAWhC,SAAAqD,SAAA;QAAA,OAAAvD,YAAA,GAAAC,CAAA,WAAAuD,SAAA;UAAA,kBAAAA,SAAA,CAAApF,CAAA;YAAA;cAAA,KACQ,IAAI,CAACqD,WAAW;gBAAA+B,SAAA,CAAApF,CAAA;gBAAA;cAAA;cAChB,IAAI,CAACqF,aAAa,CAAC,CAAC;cAACD,SAAA,CAAApF,CAAA;cAAA;YAAA;cAAAoF,SAAA,CAAApF,CAAA;cAAA,OAEf,IAAI,CAACsF,cAAc,CAAC,CAAC;YAAA;cAAA,OAAAF,SAAA,CAAApE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA,CAElC;MAAA,SANKjB,eAAeA,CAAA;QAAA,OAAAgB,gBAAA,CAAA1C,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAf0P,eAAe;IAAA;EAAA;IAAA7O,GAAA;IAAAC,KAAA;MAAA,IAAAiQ,eAAA,GAAAhD,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAQrB,SAAA0D,SAAA;QAAA,IAAAtH,MAAA;QAAA,IAAAsG,MAAA,EAAAiB,QAAA,EAAAC,GAAA;QAAA,OAAA9D,YAAA,GAAAC,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,CAAA,GAAA+E,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA+E,SAAA,CAAA3F,CAAA;cAAA,OAG6B2E,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;gBACrDxJ,KAAK,EAAE;kBACHuK,gBAAgB,EAAE,IAAI;kBACtBC,gBAAgB,EAAE,IAAI;kBACtBC,UAAU,EAAE,KAAK;kBAAE;kBACnBC,YAAY,EAAE,CAAC,CAAK;gBACxB;cACJ,CAAC,CAAC;YAAA;cAPIvB,MAAM,GAAAmB,SAAA,CAAA5E,CAAA;cASZ;cACI0E,QAAQ,GAAG,wBAAwB;cACvC,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;gBAC1CA,QAAQ,GAAG,YAAY;gBACvB,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;kBAC1CA,QAAQ,GAAG,WAAW;kBACtB,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;oBAC1CA,QAAQ,GAAG,EAAE,CAAC,CAAC;kBACnB;gBACJ;cACJ;cAEA,IAAI,CAACnC,aAAa,GAAG,IAAI0C,aAAa,CAACxB,MAAM,EAAE;gBAC3CiB,QAAQ,EAAEA;cACd,CAAC,CAAC;;cAEF;cACA,IAAI,CAACS,cAAc,GAAGT,QAAQ;;cAE9B;cACA,IAAI,CAAClC,WAAW,GAAG,EAAE;;cAErB;cACA,IAAI,CAACD,aAAa,CAAC6C,eAAe,GAAG,UAACxN,KAAK,EAAK;gBAC5C,IAAIA,KAAK,CAACyN,IAAI,CAACtR,IAAI,GAAG,CAAC,EAAE;kBACrBoJ,MAAI,CAACqF,WAAW,CAAC8C,IAAI,CAAC1N,KAAK,CAACyN,IAAI,CAAC;gBACrC;cACJ,CAAC;cAED,IAAI,CAAC9C,aAAa,CAACgD,MAAM,GAAG,YAAM;gBAC9BpI,MAAI,CAACqI,gBAAgB,CAAC,CAAC;cAC3B,CAAC;;cAED;cACA,IAAI,CAACjD,aAAa,CAACkD,KAAK,CAAC,CAAC;cAC1B,IAAI,CAACnD,WAAW,GAAG,IAAI;cACvB,IAAI,CAACG,SAAS,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC;;cAE3B;cACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;cAC5B,IAAI,CAACC,iBAAiB,CAAC,CAAC;cACxB,IAAI,CAACC,UAAU,CAAC,CAAC;cAAClB,SAAA,CAAA3F,CAAA;cAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA8E,GAAA,GAAAC,SAAA,CAAA5E,CAAA;cAElB1J,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAAoO,GAAO,CAAC;cACjD,IAAI,CAACT,SAAS,CAAC,sEAAsE,CAAC;YAAC;cAAA,OAAAU,SAAA,CAAA3E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAE9F;MAAA,SA1DKF,cAAcA,CAAA;QAAA,OAAAC,eAAA,CAAA/C,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAd8Q,cAAc;IAAA;EAAA;IAAAjQ,GAAA;IAAAC,KAAA,EA4DpB,SAAA+P,aAAaA,CAAA,EAAG;MACZ,IAAI,IAAI,CAAC/B,aAAa,IAAI,IAAI,CAACD,WAAW,EAAE;QACxC,IAAI,CAACC,aAAa,CAAC0B,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC3B,WAAW,GAAG,KAAK;;QAExB;QACA,IAAI,IAAI,CAACC,aAAa,CAACkB,MAAM,EAAE;UAC3B,IAAI,CAAClB,aAAa,CAACkB,MAAM,CAACM,SAAS,CAAC,CAAC,CAACxM,OAAO,CAAC,UAAAyM,KAAK;YAAA,OAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;UAAA,EAAC;QACxE;;QAEA;QACA,IAAI,CAACf,qBAAqB,CAAC,CAAC;QAC5B,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,SAAS,CAAC,CAAC;QAEhBzP,OAAO,CAACwE,GAAG,CAAC,mBAAmB,CAAC;MACpC;IACJ;EAAC;IAAAxG,GAAA;IAAAC,KAAA,EAED,SAAAiR,gBAAgBA,CAAA,EAAG;MAAA,IAAAQ,MAAA;MACf,IAAI,IAAI,CAACxD,WAAW,CAAC9O,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACwQ,SAAS,CAAC,wBAAwB,CAAC;QACxC;MACJ;;MAEA;MACA,IAAM+B,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC1D,WAAW,EAAE;QACzCxE,IAAI,EAAE,IAAI,CAACmH,cAAc,IAAI;MACjC,CAAC,CAAC;;MAEF;MACA,IAAI,CAACgB,YAAY,CAACF,SAAS,CAAC,CAAC1E,IAAI,CAAC,UAAA6E,OAAO,EAAI;QACzCJ,MAAI,CAACK,gBAAgB,CAACD,OAAO,CAAC;MAClC,CAAC,CAAC,SAAM,CAAC,UAAA7P,KAAK,EAAI;QACdD,OAAO,CAAC2M,IAAI,CAAC,+CAA+C,EAAE1M,KAAK,CAAC;QACpE;QACAyP,MAAI,CAACK,gBAAgB,CAACJ,SAAS,CAAC;MACpC,CAAC,CAAC;IACN;EAAC;IAAA3R,GAAA;IAAAC,KAAA;MAAA,IAAA+R,aAAA,GAAA9E,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAwF,SAAmBN,SAAS;QAAA,IAAAO,MAAA;QAAA,OAAA3F,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,CAAA;YAAA;cAAA,OAAAwH,SAAA,CAAAxG,CAAA,IACjB,IAAIhK,OAAO,CAAC,UAACC,OAAO,EAAEwQ,MAAM,EAAK;gBACpC,IAAMC,YAAY,GAAG,KAAK7P,MAAM,CAAC8P,YAAY,IAAI9P,MAAM,CAAC+P,kBAAkB,EAAE,CAAC;gBAC7E,IAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;gBAEnCD,UAAU,CAACE,MAAM;kBAAA,IAAAC,KAAA,GAAAzF,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAAG,SAAAmG,SAAOnO,CAAC;oBAAA,IAAAoO,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAjB,OAAA,EAAAkB,GAAA;oBAAA,OAAAzG,YAAA,GAAAC,CAAA,WAAAyG,SAAA;sBAAA,kBAAAA,SAAA,CAAA1H,CAAA,GAAA0H,SAAA,CAAAtI,CAAA;wBAAA;0BAAAsI,SAAA,CAAA1H,CAAA;0BAEpB;0BACMsH,WAAW,GAAGpO,CAAC,CAAC8F,MAAM,CAAC2I,MAAM;0BAAAD,SAAA,CAAAtI,CAAA;0BAAA,OACT0H,YAAY,CAACc,eAAe,CAACN,WAAW,CAAC;wBAAA;0BAA7DC,WAAW,GAAAG,SAAA,CAAAvH,CAAA;0BAEjB;0BACMqH,aAAa,GAAGb,MAAI,CAACkB,WAAW,CAACN,WAAW,EAAET,YAAY,CAAC,EAEjE;0BACMP,OAAO,GAAGI,MAAI,CAACmB,gBAAgB,CAACN,aAAa,CAAC;0BACpDnR,OAAO,CAACkQ,OAAO,CAAC;0BAACmB,SAAA,CAAAtI,CAAA;0BAAA;wBAAA;0BAAAsI,SAAA,CAAA1H,CAAA;0BAAAyH,GAAA,GAAAC,SAAA,CAAAvH,CAAA;0BAEjB0G,MAAM,CAAAY,GAAM,CAAC;wBAAC;0BAAA,OAAAC,SAAA,CAAAtH,CAAA;sBAAA;oBAAA,GAAAiH,QAAA;kBAAA,CAErB;kBAAA,iBAAAU,GAAA;oBAAA,OAAAX,KAAA,CAAAxF,KAAA,OAAAhO,SAAA;kBAAA;gBAAA;gBAEDqT,UAAU,CAACe,OAAO,GAAG;kBAAA,OAAMnB,MAAM,CAAC,IAAIhJ,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAAA;gBACzEoJ,UAAU,CAACgB,iBAAiB,CAAC7B,SAAS,CAAC;cAC3C,CAAC,CAAC;UAAA;QAAA,GAAAM,QAAA;MAAA,CACL;MAAA,SAzBKJ,YAAYA,CAAA4B,EAAA;QAAA,OAAAzB,aAAA,CAAA7E,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAZ0S,YAAY;IAAA;IA2BlB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAA7R,GAAA;IAAAC,KAAA,EAQA,SAAAmT,WAAWA,CAACN,WAAW,EAAET,YAAY,EAA2C;MAAA,IAAzCqB,gBAAgB,GAAAvU,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,IAAEwU,QAAQ,GAAAxU,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;MAC1E,IAAMsR,UAAU,GAAGqC,WAAW,CAACrC,UAAU;MACzC,IAAMmD,gBAAgB,GAAGd,WAAW,CAACc,gBAAgB;MACrD,IAAMxU,MAAM,GAAG0T,WAAW,CAAC1T,MAAM;;MAEjC;MACA,IAAMyU,aAAa,GAAG1R,IAAI,CAACoG,KAAK,CAAEoL,QAAQ,GAAG,IAAI,GAAIlD,UAAU,CAAC;;MAEhE;MACA,IAAMqD,SAAS,GAAGhB,WAAW,CAACiB,cAAc,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAIC,UAAU,GAAG,CAAC;MAClB,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjJ,MAAM,EAAEiJ,CAAC,EAAE,EAAE;QAC7B,IAAIlG,IAAI,CAAC8R,GAAG,CAACH,SAAS,CAACzL,CAAC,CAAC,CAAC,GAAGqL,gBAAgB,EAAE;UAC3CM,UAAU,GAAG7R,IAAI,CAAC+R,GAAG,CAAC,CAAC,EAAE7L,CAAC,GAAGwL,aAAa,CAAC;UAC3C;QACJ;MACJ;;MAEA;MACA,IAAIM,QAAQ,GAAG/U,MAAM,GAAG,CAAC;MACzB,KAAK,IAAIiJ,EAAC,GAAGjJ,MAAM,GAAG,CAAC,EAAEiJ,EAAC,IAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;QAClC,IAAIlG,IAAI,CAAC8R,GAAG,CAACH,SAAS,CAACzL,EAAC,CAAC,CAAC,GAAGqL,gBAAgB,EAAE;UAC3CS,QAAQ,GAAGhS,IAAI,CAACiS,GAAG,CAAChV,MAAM,GAAG,CAAC,EAAEiJ,EAAC,GAAGwL,aAAa,CAAC;UAClD;QACJ;MACJ;;MAEA;MACA,IAAIG,UAAU,IAAIG,QAAQ,EAAE;QACxBnS,OAAO,CAAC2M,IAAI,CAAC,uDAAuD,CAAC;QACrE,IAAM0F,aAAa,GAAGlS,IAAI,CAACoG,KAAK,CAACkI,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QACpD,IAAM6D,aAAa,GAAGjC,YAAY,CAACkC,YAAY,CAC3CX,gBAAgB,EAChBS,aAAa,EACb5D,UACJ,CAAC;QACD,OAAO6D,aAAa;MACxB;;MAEA;MACA,IAAME,SAAS,GAAGL,QAAQ,GAAGH,UAAU,GAAG,CAAC;;MAE3C;MACA,IAAMjB,aAAa,GAAGV,YAAY,CAACkC,YAAY,CAC3CX,gBAAgB,EAChBY,SAAS,EACT/D,UACJ,CAAC;;MAED;MACA,KAAK,IAAIgE,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGb,gBAAgB,EAAEa,OAAO,EAAE,EAAE;QACzD,IAAMC,YAAY,GAAG5B,WAAW,CAACiB,cAAc,CAACU,OAAO,CAAC;QACxD,IAAME,WAAW,GAAG5B,aAAa,CAACgB,cAAc,CAACU,OAAO,CAAC;QAEzD,KAAK,IAAIpM,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGmM,SAAS,EAAEnM,GAAC,EAAE,EAAE;UAChCsM,WAAW,CAACtM,GAAC,CAAC,GAAGqM,YAAY,CAACV,UAAU,GAAG3L,GAAC,CAAC;QACjD;MACJ;;MAEA;MACA,IAAMuM,gBAAgB,GAAG,CAACxV,MAAM,GAAGqR,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAChE,IAAMC,eAAe,GAAG,CAACN,SAAS,GAAG/D,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAClE,IAAME,YAAY,GAAG,CAACf,UAAU,GAAGvD,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAChE,IAAMG,UAAU,GAAG,CAAC,CAAC5V,MAAM,GAAG+U,QAAQ,GAAG,CAAC,IAAI1D,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAE3E7S,OAAO,CAACwE,GAAG,mBAAA5F,MAAA,CAAmBgU,gBAAgB,gBAAAhU,MAAA,CAAQkU,eAAe,kBAAAlU,MAAA,CAAemU,YAAY,qBAAAnU,MAAA,CAAkBoU,UAAU,iBAAc,CAAC;MAE3I,OAAOjC,aAAa;IACxB;EAAC;IAAA/S,GAAA;IAAAC,KAAA,EAED,SAAAoT,gBAAgBA,CAAC4B,MAAM,EAAE;MACrB;MACA,IAAMC,gBAAgB,GAAG,KAAK,CAAC,CAAC;MAChC,IAAMC,cAAc,GAAG,CAAC,CAAC,CAAO;MAChC,IAAMC,aAAa,GAAG,EAAE;;MAExB;MACA,IAAMC,eAAe,GAAG,IAAI,CAACC,wBAAwB,CAACL,MAAM,EAAEC,gBAAgB,CAAC;MAE/E,IAAM9V,MAAM,GAAGiW,eAAe,CAACjW,MAAM;MACrC,IAAMwU,gBAAgB,GAAGuB,cAAc;MACvC,IAAM1E,UAAU,GAAGyE,gBAAgB;MACnC,IAAMK,cAAc,GAAGH,aAAa,GAAG,CAAC;MACxC,IAAMI,UAAU,GAAG5B,gBAAgB,GAAG2B,cAAc;MACpD,IAAME,QAAQ,GAAGhF,UAAU,GAAG+E,UAAU,CAAC,CAAC;MAC1C,IAAME,QAAQ,GAAGtW,MAAM,GAAGoW,UAAU;MACpC,IAAMG,UAAU,GAAG,EAAE,GAAGD,QAAQ;MAEhC,IAAM7C,WAAW,GAAG,IAAI+C,WAAW,CAACD,UAAU,CAAC;MAC/C,IAAME,IAAI,GAAG,IAAIC,QAAQ,CAACjD,WAAW,CAAC;;MAEtC;MACA,IAAMkD,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAAM,EAAEC,MAAM,EAAK;QACpC,KAAK,IAAI5N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,MAAM,CAAC7W,MAAM,EAAEiJ,CAAC,EAAE,EAAE;UACpCwN,IAAI,CAACK,QAAQ,CAACF,MAAM,GAAG3N,CAAC,EAAE4N,MAAM,CAACE,UAAU,CAAC9N,CAAC,CAAC,CAAC;QACnD;MACJ,CAAC;;MAED;MACA0N,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;MACtBF,IAAI,CAACO,SAAS,CAAC,CAAC,EAAET,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC;MACvCI,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;;MAEtB;MACAA,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC;MACvBF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;MAC9BP,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MAC7BR,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEzC,gBAAgB,EAAE,IAAI,CAAC;MAC1CiC,IAAI,CAACO,SAAS,CAAC,EAAE,EAAE3F,UAAU,EAAE,IAAI,CAAC;MACpCoF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAEX,QAAQ,EAAE,IAAI,CAAC;MAClCI,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEb,UAAU,EAAE,IAAI,CAAC;MACpCK,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEjB,aAAa,EAAE,IAAI,CAAC;;MAEvC;MACAW,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC;MACvBF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAEV,QAAQ,EAAE,IAAI,CAAC;;MAElC;MACA,IAAIM,MAAM,GAAG,EAAE;MACf,KAAK,IAAI3N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjJ,MAAM,EAAEiJ,CAAC,EAAE,EAAE;QAC7B,IAAMiO,MAAM,GAAGnU,IAAI,CAAC+R,GAAG,CAAC,CAAC,CAAC,EAAE/R,IAAI,CAACiS,GAAG,CAAC,CAAC,EAAEiB,eAAe,CAAChN,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAMkO,SAAS,GAAGD,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,MAAM,GAAGA,MAAM,GAAG,MAAM;QAChET,IAAI,CAACW,QAAQ,CAACR,MAAM,EAAEO,SAAS,EAAE,IAAI,CAAC;QACtCP,MAAM,IAAI,CAAC;MACf;MAEA,OAAO,IAAIpE,IAAI,CAAC,CAACiB,WAAW,CAAC,EAAE;QAAEnJ,IAAI,EAAE;MAAY,CAAC,CAAC;IACzD;EAAC;IAAA1J,GAAA;IAAAC,KAAA,EAED,SAAAqV,wBAAwBA,CAACL,MAAM,EAAEC,gBAAgB,EAAE;MAC/C,IAAMuB,kBAAkB,GAAGxB,MAAM,CAACxE,UAAU;MAC5C,IAAMiG,cAAc,GAAGzB,MAAM,CAAC7V,MAAM;MACpC,IAAMwU,gBAAgB,GAAGqB,MAAM,CAACrB,gBAAgB;;MAEhD;MACA,IAAM+C,aAAa,GAAGzB,gBAAgB,GAAGuB,kBAAkB;MAC3D,IAAMjC,SAAS,GAAGrS,IAAI,CAACoG,KAAK,CAACmO,cAAc,GAAGC,aAAa,CAAC;;MAE5D;MACA,IAAMC,aAAa,GAAG,IAAIC,YAAY,CAACrC,SAAS,CAAC;;MAEjD;MACA,IAAMsC,QAAQ,GAAG,IAAID,YAAY,CAACH,cAAc,CAAC;MACjD,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,cAAc,EAAErO,CAAC,EAAE,EAAE;QACrC,IAAI0O,GAAG,GAAG,CAAC;QACX,KAAK,IAAItC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGb,gBAAgB,EAAEa,OAAO,EAAE,EAAE;UACzDsC,GAAG,IAAI9B,MAAM,CAAClB,cAAc,CAACU,OAAO,CAAC,CAACpM,CAAC,CAAC;QAC5C;QACAyO,QAAQ,CAACzO,CAAC,CAAC,GAAG0O,GAAG,GAAGnD,gBAAgB,CAAC,CAAC;MAC1C;;MAEA;MACA,KAAK,IAAIvL,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGmM,SAAS,EAAEnM,GAAC,EAAE,EAAE;QAChC,IAAM2O,aAAa,GAAG3O,GAAC,GAAGsO,aAAa;QACvC,IAAMtR,KAAK,GAAGlD,IAAI,CAACoG,KAAK,CAACyO,aAAa,CAAC;QACvC,IAAMC,QAAQ,GAAGD,aAAa,GAAG3R,KAAK;QAEtC,IAAIA,KAAK,GAAG,CAAC,GAAGqR,cAAc,EAAE;UAC5B;UACAE,aAAa,CAACvO,GAAC,CAAC,GAAGyO,QAAQ,CAACzR,KAAK,CAAC,IAAI,CAAC,GAAG4R,QAAQ,CAAC,GAAGH,QAAQ,CAACzR,KAAK,GAAG,CAAC,CAAC,GAAG4R,QAAQ;QACxF,CAAC,MAAM;UACH;UACAL,aAAa,CAACvO,GAAC,CAAC,GAAGyO,QAAQ,CAACzR,KAAK,CAAC,IAAI,CAAC;QAC3C;MACJ;MAEA,OAAOuR,aAAa;IACxB;EAAC;IAAA5W,GAAA;IAAAC,KAAA;MAAA,IAAAiX,iBAAA,GAAAhK,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAA0K,SAAuBxF,SAAS;QAAA,IAAAyF,MAAA,EAAAlE,MAAA,EAAAmE,GAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAA8K,SAAA;UAAA,kBAAAA,SAAA,CAAA/L,CAAA,GAAA+L,SAAA,CAAA3M,CAAA;YAAA;cAAA2M,SAAA,CAAA/L,CAAA;cAExB;cACA,IAAI,CAACgM,gBAAgB,CAAC,CAAC;;cAEvB;cACAvV,OAAO,CAACwE,GAAG,CAACgR,aAAa,CAAC;cACpBJ,MAAM,GAAG;gBACXK,QAAQ,EAAED,aAAa,CAACC,QAAQ,IAAI,EAAE;gBAAE;gBACxCC,OAAO,EAAEF,aAAa,CAACE,OAAO,IAAI,EAAE;gBACpCC,SAAS,EAAEH,aAAa,CAACG,SAAS,IAAI;cAC1C,CAAC,EAED;cAAAL,SAAA,CAAA3M,CAAA;cAAA,OACqB,IAAI,CAAC0D,oBAAoB,CAACuJ,cAAc,CAACjG,SAAS,EAAEyF,MAAM,CAAC;YAAA;cAA1ElE,MAAM,GAAAoE,SAAA,CAAA5L,CAAA;cAEZ;cACA,IAAI,CAACmM,cAAc,CAAC3E,MAAM,CAAC;cAACoE,SAAA,CAAA3M,CAAA;cAAA;YAAA;cAAA2M,SAAA,CAAA/L,CAAA;cAAA8L,GAAA,GAAAC,SAAA,CAAA5L,CAAA;cAG5B1J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAAoV,GAAO,CAAC;cACnD,IAAI,CAACzH,SAAS,CAACyH,GAAA,CAAMS,OAAO,IAAI,wCAAwC,CAAC;YAAC;cAAAR,SAAA,CAAA/L,CAAA;cAE1E,IAAI,CAACwM,gBAAgB,CAAC,CAAC;cAAC,OAAAT,SAAA,CAAAhM,CAAA;YAAA;cAAA,OAAAgM,SAAA,CAAA3L,CAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA,CAE/B;MAAA,SAzBKpF,gBAAgBA,CAAAiG,GAAA;QAAA,OAAAd,iBAAA,CAAA/J,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAhB4S,gBAAgB;IAAA;EAAA;IAAA/R,GAAA;IAAAC,KAAA,EA2BtB,SAAA4X,cAAcA,CAAC9G,IAAI,EAAE;MACjB;MACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACkH,QAAQ,EAAE;QACzB,IAAI,CAACrI,SAAS,CAAC,8CAA8C,CAAC;QAC9D;MACJ;MAEA,IAAMqI,QAAQ,GAAGlH,IAAI,CAACkH,QAAQ;MAC9B,IAAMC,MAAM,GAAGD,QAAQ,CAACC,MAAM,IAAI,CAAC,CAAC;MACpC,IAAMC,QAAQ,GAAGpH,IAAI,CAACqH,SAAS,IAAI,CAAC,CAAC;;MAErC;MACA,IAAMC,YAAY,GAAGlW,IAAI,CAACmW,KAAK,CAACL,QAAQ,CAACM,aAAa,IAAI,CAAC,CAAC;MAC5D,IAAMC,QAAQ,GAAGrW,IAAI,CAACmW,KAAK,CAACJ,MAAM,CAACM,QAAQ,IAAI,CAAC,CAAC;MACjD,IAAMC,OAAO,GAAGtW,IAAI,CAACmW,KAAK,CAACJ,MAAM,CAACO,OAAO,IAAI,CAAC,CAAC;MAC/C,IAAMC,YAAY,GAAGvW,IAAI,CAACmW,KAAK,CAACJ,MAAM,CAACQ,YAAY,IAAI,CAAC,CAAC;MACzD,IAAMC,aAAa,GAAGxW,IAAI,CAACmW,KAAK,CAACJ,MAAM,CAACS,aAAa,IAAI,CAAC,CAAC;;MAE3D;MACA,IAAMC,YAAY,GAAG7H,IAAI,CAAC8H,aAAa,IAAIV,QAAQ,CAACW,IAAI,IAAI,SAAS;MACrE,IAAMC,cAAc,GAAGd,QAAQ,CAACe,eAAe,IAAI,gBAAgB;MACnE,IAAMC,QAAQ,GAAGhB,QAAQ,CAACgB,QAAQ,IAAI,uBAAuB;;MAE7D;MACA,IAAMC,cAAc,GAAGjB,QAAQ,CAACkB,eAAe,IAAI,EAAE;MACrD,IAAMC,WAAW,GAAGnB,QAAQ,CAACmB,WAAW,IAAI,EAAE;;MAE9C;MACA,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIH,cAAc,CAAC9Z,MAAM,GAAG,CAAC,EAAE;QAC3Bia,UAAU,0JAAAzY,MAAA,CAIIsY,cAAc,CAAC9Q,GAAG,CAAC,UAAAkR,KAAK;UAAA,cAAA1Y,MAAA,CAAW0Y,KAAK;QAAA,CAAO,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,sEAGtE;MACL;MAEA,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIJ,WAAW,CAACha,MAAM,GAAG,CAAC,EAAE;QACxBoa,eAAe,kKAAA5Y,MAAA,CAIDwY,WAAW,CAAChR,GAAG,CAAC,UAAAqR,UAAU;UAAA,cAAA7Y,MAAA,CAAW6Y,UAAU;QAAA,CAAO,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC,sEAG7E;MACL;;MAEA;MACA,IAAMG,cAAc,GAAGxZ,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIqY,cAAc,EAAE;QAChBA,cAAc,CAAC5T,SAAS,8WAAAlF,MAAA,CAOoBgY,YAAY,4OAAAhY,MAAA,CAIZmY,cAAc,6MAAAnY,MAAA,CAKd,IAAI,CAAC+Y,aAAa,CAACtB,YAAY,CAAC,mVAAAzX,MAAA,CAIeyX,YAAY,GAAG,GAAG,qPAAAzX,MAAA,CAGlBuB,IAAI,CAACiS,GAAG,CAAC,GAAG,EAAEiE,YAAY,GAAG,GAAG,CAAC,oOAAAzX,MAAA,CAGnDyX,YAAY,yoBAAAzX,MAAA,CAYvC,IAAI,CAAC+Y,aAAa,CAACnB,QAAQ,CAAC,wBAAA5X,MAAA,CAAmB4X,QAAQ,+GAAA5X,MAAA,CAExD4X,QAAQ,2RAAA5X,MAAA,CAKP,IAAI,CAAC+Y,aAAa,CAAClB,OAAO,CAAC,wBAAA7X,MAAA,CAAmB6X,OAAO,+GAAA7X,MAAA,CAEtD6X,OAAO,gSAAA7X,MAAA,CAKN,IAAI,CAAC+Y,aAAa,CAACjB,YAAY,CAAC,wBAAA9X,MAAA,CAAmB8X,YAAY,+GAAA9X,MAAA,CAEhE8X,YAAY,iSAAA9X,MAAA,CAKX,IAAI,CAAC+Y,aAAa,CAAChB,aAAa,CAAC,wBAAA/X,MAAA,CAAmB+X,aAAa,+GAAA/X,MAAA,CAElE+X,aAAa,4NAAA/X,MAAA,CAMzCqY,QAAQ,kFAAArY,MAAA,CAGfyY,UAAU,4BAAAzY,MAAA,CACV4Y,eAAe,yYAOxB;MACL;MAEAxX,OAAO,CAACwE,GAAG,CAAC,kCAAkC,EAAE;QAC5C6R,YAAY,EAAZA,YAAY;QACZH,MAAM,EAAE;UAAEM,QAAQ,EAARA,QAAQ;UAAEC,OAAO,EAAPA,OAAO;UAAEC,YAAY,EAAZA,YAAY;UAAEC,aAAa,EAAbA;QAAc,CAAC;QAC1DC,YAAY,EAAZA,YAAY;QACZG,cAAc,EAAdA,cAAc;QACdE,QAAQ,EAARA,QAAQ;QACRC,cAAc,EAAdA,cAAc;QACdE,WAAW,EAAXA;MACJ,CAAC,CAAC;IACN;EAAC;IAAApZ,GAAA;IAAAC,KAAA,EAED,SAAA0Z,aAAaA,CAACC,KAAK,EAAE;MACjB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;MACzC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,YAAY;MACpC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,YAAY;MACpC,OAAO,YAAY;IACvB;EAAC;IAAA5Z,GAAA;IAAAC,KAAA,EAED,SAAAqR,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAC/C,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAACpN,KAAK,CAACC,OAAO,GAAG,OAAO;MAChD;MACA,IAAI,IAAI,CAACoN,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACrN,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;IACJ;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAA2O,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACL,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAACpN,KAAK,CAACC,OAAO,GAAG,MAAM;MAC/C;MACA,IAAI,IAAI,CAACoN,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACrN,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;IACJ;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAAsR,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACjD,SAAS,EAAE;QAChB,IAAMuL,UAAU,GAAG,IAAI,CAACvL,SAAS,CAACjN,aAAa,CAAC,cAAc,CAAC;QAC/D,IAAI,IAAI,CAAC2M,WAAW,EAAE;UAClB,IAAI,CAACM,SAAS,CAAC/N,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;UACzC,IAAIqZ,UAAU,EAAEA,UAAU,CAAC9Y,WAAW,GAAG,gBAAgB;QAC7D,CAAC,MAAM;UACH,IAAI,CAACuN,SAAS,CAAC/N,SAAS,CAACgD,MAAM,CAAC,WAAW,CAAC;UAC5C,IAAIsW,UAAU,EAAEA,UAAU,CAAC9Y,WAAW,GAAG,YAAY;QACzD;MACJ;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAuR,UAAUA,CAAA,EAAG;MAAA,IAAAsI,MAAA;MACT,IAAI,CAAC1L,aAAa,GAAG2L,WAAW,CAAC,YAAM;QACnC,IAAID,MAAI,CAAC3L,SAAS,IAAI2L,MAAI,CAACrL,YAAY,EAAE;UACrC,IAAMuL,OAAO,GAAG5I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGyI,MAAI,CAAC3L,SAAS;UAC3C,IAAM8L,OAAO,GAAG9X,IAAI,CAACoG,KAAK,CAACyR,OAAO,GAAG,KAAK,CAAC;UAC3C,IAAME,OAAO,GAAG/X,IAAI,CAACoG,KAAK,CAAEyR,OAAO,GAAG,KAAK,GAAI,IAAI,CAAC;UAEpD,IAAMG,UAAU,MAAAvZ,MAAA,CAAMqZ,OAAO,CAAC5X,QAAQ,CAAC,CAAC,CAAC+X,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAAxZ,MAAA,CAAIsZ,OAAO,CAAC7X,QAAQ,CAAC,CAAC,CAAC+X,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAE;UAClGN,MAAI,CAACrL,YAAY,CAAC1N,WAAW,GAAGoZ,UAAU;QAC9C;MACJ,CAAC,EAAE,GAAG,CAAC;IACX;EAAC;IAAAna,GAAA;IAAAC,KAAA,EAED,SAAAwR,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACrD,aAAa,EAAE;QACpBiM,aAAa,CAAC,IAAI,CAACjM,aAAa,CAAC;QACjC,IAAI,CAACA,aAAa,GAAG,IAAI;MAC7B;IACJ;EAAC;IAAApO,GAAA;IAAAC,KAAA,EAED,SAAAsX,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACjJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC3H,QAAQ,GAAG,IAAI;QAC9B,IAAMkT,UAAU,GAAG,IAAI,CAACvL,SAAS,CAACjN,aAAa,CAAC,cAAc,CAAC;QAC/D,IAAIwY,UAAU,EAAEA,UAAU,CAAC9Y,WAAW,GAAG,eAAe;MAC5D;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAA8X,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACzJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC3H,QAAQ,GAAG,KAAK;QAC/B,IAAI,CAAC4K,iBAAiB,CAAC,CAAC;MAC5B;IACJ;EAAC;IAAAvR,GAAA;IAAAC,KAAA,EAED,SAAA2P,SAASA,CAACkI,OAAO,EAAE;MACf9V,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAE6V,OAAO,CAAC;;MAEtD;MACA,IAAM4B,cAAc,GAAGxZ,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIqY,cAAc,EAAE;QAChBA,cAAc,CAAC5T,SAAS,kHAAAlF,MAAA,CAGXkX,OAAO,4KAGnB;MACL;IACJ;EAAC;IAAA9X,GAAA;IAAAC,KAAA,EAED,SAAAqa,cAAcA,CAAA,EAAG;MACb;MACA,IAAI,CAACtM,WAAW,GAAG,KAAK;MACxB,IAAI,CAACE,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,SAAS,GAAG,IAAI;;MAErB;MACA,IAAI,CAACsD,SAAS,CAAC,CAAC;;MAEhB;MACA,IAAI,CAAC7C,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;;MAExB;MACA,IAAMmI,cAAc,GAAGxZ,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIqY,cAAc,EAAE;QAChBA,cAAc,CAAC5T,SAAS,GAAG,EAAE;MACjC;;MAEA;MACA,IAAI,IAAI,CAAC2I,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC1N,WAAW,GAAG,OAAO;MAC3C;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAsa,QAAQA,CAAA,EAAG;MACP;MACA,OAAO/X,MAAM,CAACgY,gBAAgB,IAAI,EAAE;IACxC;EAAC;IAAAxa,GAAA;IAAAC,KAAA,EAED,SAAA8O,gBAAgBA,CAACtK,CAAC,EAAE;MAChB,IAAMkB,MAAM,GAAGlB,CAAC,CAAC8F,MAAM,CAAChF,OAAO,CAACI,MAAM;MACtC,IAAM8U,OAAO,GAAG,IAAI,CAAC1M,aAAa;MAClC,IAAI,CAAC2M,aAAa,CAAC;QAAC/U,MAAM,EAANA,MAAM;QAAE8U,OAAO,EAAPA;MAAO,CAAC,CAAC;IACzC;EAAC;IAAAza,GAAA;IAAAC,KAAA;MAAA,IAAA0a,cAAA,GAAAzN,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAmO,SAAAC,KAAA;QAAA,IAAAlV,MAAA,EAAA8U,OAAA,EAAAlU,QAAA,EAAAuU,GAAA;QAAA,OAAAvO,YAAA,GAAAC,CAAA,WAAAuO,SAAA;UAAA,kBAAAA,SAAA,CAAAxP,CAAA,GAAAwP,SAAA,CAAApQ,CAAA;YAAA;cAAqBhF,MAAM,GAAAkV,KAAA,CAANlV,MAAM,EAAE8U,OAAO,GAAAI,KAAA,CAAPJ,OAAO;cAAAM,SAAA,CAAAxP,CAAA;cAAAwP,SAAA,CAAApQ,CAAA;cAAA,OAEL,IAAI,CAAC0D,oBAAoB,CAAC2M,cAAc,CAAC;gBAACrV,MAAM,EAANA,MAAM;gBAAE8U,OAAO,EAAPA;cAAO,CAAC,CAAC;YAAA;cAA5ElU,QAAQ,GAAAwU,SAAA,CAAArP,CAAA;cACd1J,OAAO,CAACwE,GAAG,CAACD,QAAQ,CAAC;cAACwU,SAAA,CAAApQ,CAAA;cAAA;YAAA;cAAAoQ,SAAA,CAAAxP,CAAA;cAAAuP,GAAA,GAAAC,SAAA,CAAArP,CAAA;cAEtB1J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAA6Y,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAApP,CAAA;UAAA;QAAA,GAAAiP,QAAA;MAAA,CAG3D;MAAA,SARKF,aAAaA,CAAAO,GAAA;QAAA,OAAAN,cAAA,CAAAxN,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAbub,aAAa;IAAA;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;AC9pBvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE0C;AACQ;AACgB;AAAA,IAE5DS,cAAc;EAChB;AACJ;AACA;AACA;AACA;EACI,SAAAA,eAAYpZ,OAAO,EAAE;IAAAzC,eAAA,OAAA6b,cAAA;IACjB;IACA,IAAI,CAACpZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqZ,QAAQ,GAAG5Y,MAAM,CAAC6Y,aAAa,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,QAAQ,CAACG,QAAQ,IAAI,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAAC7W,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAAC8W,YAAY,GAAG,KAAK;IACzB,IAAI,CAACtV,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,CAACuV,QAAQ,GAAGxb,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IAC1D,IAAI,CAACwb,YAAY,GAAGzb,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACyb,YAAY,GAAG1b,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAAC0b,YAAY,GAAG3b,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;;IAElE;IACA,IAAI,CAAC2b,iBAAiB,GAAG5b,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC;IAC5E,IAAI,CAAC4b,WAAW,GAAG7b,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;IAChE,IAAI,CAAC6b,YAAY,GAAG9b,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAAC8b,mBAAmB,GAAG/b,QAAQ,CAACC,cAAc,CAAC,4BAA4B,CAAC;IAChF,IAAI,CAAC+b,cAAc,GAAGhc,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;IACtE,IAAI,CAACgc,YAAY,GAAGjc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACic,eAAe,GAAGlc,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;;IAExE;IACA,IAAI,CAACkc,UAAU,GAAGnc,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAACmc,UAAU,GAAGpc,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAACuG,YAAY,GAAGxG,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAC/D,IAAI,CAACoc,kBAAkB,GAAGrc,QAAQ,CAACC,cAAc,CAAC,2BAA2B,CAAC;IAC9E,IAAI,CAACqc,sBAAsB,GAAGtc,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;IAC/E,IAAI,CAACsc,qBAAqB,GAAGvc,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;;IAE7E;IACA,IAAI,CAACuc,gBAAgB,GAAGxc,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC;IAC1E,IAAI,CAACwc,cAAc,GAAGzc,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;IACtE,IAAI,CAACyc,YAAY,GAAG1c,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;;IAE/D;IACA,IAAI,CAAC0c,SAAS,GAAG,IAAI,CAACzB,QAAQ,CAAC0B,UAAU,IAAI,IAAI,CAAC,CAAC;IACnD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,SAAS;IACnC,IAAI,CAACzO,aAAa,GAAG,IAAI;;IAEzB;IACA,IAAI,CAACxO,MAAM,GAAGX,0DAAM,CAAC6C,UAAU,CAAC,IAAI,CAACC,OAAO,EAAE;MAC1CvC,IAAI,EAAE;IACV,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EAFI,OAAAO,YAAA,CAAAob,cAAA;IAAAnb,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACkd,aAAa,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACja,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAA/C,GAAA;IAAAC,KAAA,EAGA,SAAA+c,aAAaA,CAAA,EAAG;MAAA,IAAAtb,KAAA;MACZ,IAAI,CAAC,IAAI,CAAC0Z,QAAQ,CAAC/a,EAAE,EAAE;QACnB2B,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAC;QACpC;MACJ;MAEA,IAAI,CAACrC,MAAM,CAACqB,IAAI,CAAC,2BAA2B,CAAC;MAE7Cia,6DAAW,CAAC+B,gBAAgB,CAAC,IAAI,CAAC7B,QAAQ,CAAC/a,EAAE,CAAC,CACzC4M,IAAI,CAAC,UAAA8D,IAAI,EAAI;QACVrP,KAAI,CAAC4Z,SAAS,GAAGvK,IAAI,CAACuK,SAAS,IAAI,EAAE;QACrC5Z,KAAI,CAACwb,wBAAwB,CAAC,CAAC;QAC/Bxb,KAAI,CAACyb,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC,SACI,CAAC,UAAAlb,KAAK,EAAI;QACZD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDmb,KAAK,CAAC,kDAAkD,CAAC;MAC7D,CAAC,CAAC,WACM,CAAC,YAAM;QACX1b,KAAI,CAAC9B,MAAM,CAACoB,IAAI,CAAC,CAAC;MACtB,CAAC,CAAC;IACV;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAA8C,mBAAmBA,CAAA,EAAG;MAAA,IAAA8F,MAAA;MAClB;MACA,IAAI,IAAI,CAACwT,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAClZ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACka,oBAAoB,CAACha,IAAI,CAAC,IAAI,CAAC,CAAC;MACnF;MAEA,IAAI,IAAI,CAACiZ,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACnZ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACma,gBAAgB,CAACja,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/E;MAEA,IAAI,IAAI,CAACqD,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACvD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACoa,iBAAiB,CAACla,IAAI,CAAC,IAAI,CAAC,CAAC;MAClF;MAEA,IAAI,IAAI,CAACuZ,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACzZ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACqa,eAAe,CAACna,IAAI,CAAC,IAAI,CAAC,CAAC;MAChF;;MAEA;MACAb,MAAM,CAACW,gBAAgB,CAAC,cAAc,EAAE,UAACG,KAAK,EAAK;QAC/C,IAAIuF,MAAI,CAAC4S,YAAY,IAAI,CAAC5S,MAAI,CAAC1C,YAAY,EAAE;UACzC,IAAM2R,OAAO,GAAG,kEAAkE;UAClFxU,KAAK,CAACma,WAAW,GAAG3F,OAAO;UAC3B,OAAOA,OAAO;QAClB;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAA9X,GAAA;IAAAC,KAAA,EAGA,SAAAkd,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC7B,SAAS,CAAClc,MAAM,EAAE;QACxBge,KAAK,CAAC,uCAAuC,CAAC;QAC9C;MACJ;MAEA,IAAI,CAAC3B,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAAC7W,WAAW,GAAG,CAAC,CAAC;;MAErB;MACA,IAAI,CAAC6M,UAAU,CAAC,CAAC;;MAEjB;MACA,IAAI,CAACkM,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAA1d,GAAA;IAAAC,KAAA,EAGA,SAAAuR,UAAUA,CAAA,EAAG;MAAA,IAAAE,MAAA;MACT;MACA,IAAI,CAACqL,aAAa,GAAG,IAAI,CAACF,SAAS;MACnC,IAAI,CAACc,kBAAkB,CAAC,CAAC;;MAEzB;MACA,IAAI,IAAI,CAACvP,aAAa,EAAE;QACpBiM,aAAa,CAAC,IAAI,CAACjM,aAAa,CAAC;MACrC;;MAEA;MACA,IAAMD,SAAS,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACjD,aAAa,GAAG2L,WAAW,CAAC,YAAM;QACnC,IAAM6D,cAAc,GAAGzb,IAAI,CAACoG,KAAK,CAAC,CAAC6I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlD,SAAS,IAAI,IAAI,CAAC;QAClEuD,MAAI,CAACqL,aAAa,GAAG5a,IAAI,CAAC+R,GAAG,CAAC,CAAC,EAAExC,MAAI,CAACmL,SAAS,GAAGe,cAAc,CAAC;QAEjElM,MAAI,CAACiM,kBAAkB,CAAC,CAAC;;QAEzB;QACA,IAAIjM,MAAI,CAACqL,aAAa,IAAI,CAAC,EAAE;UACzB1C,aAAa,CAAC3I,MAAI,CAACtD,aAAa,CAAC;UACjCsD,MAAI,CAACmM,UAAU,CAAC,CAAC;QACrB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;;IAEA;AACJ;AACA;EAFI;IAAA7d,GAAA;IAAAC,KAAA,EAGA,SAAA0d,kBAAkBA,CAAA,EAAG;MACjB;MACA,IAAM1D,OAAO,GAAG9X,IAAI,CAACoG,KAAK,CAAC,IAAI,CAACwU,aAAa,GAAG,EAAE,CAAC;MACnD,IAAM7C,OAAO,GAAG,IAAI,CAAC6C,aAAa,GAAG,EAAE;;MAEvC;MACA,IAAI,IAAI,CAACnB,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC7a,WAAW,GAAGkZ,OAAO,CAAC5X,QAAQ,CAAC,CAAC,CAAC+X,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvE;MAEA,IAAI,IAAI,CAACyB,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC9a,WAAW,GAAGmZ,OAAO,CAAC7X,QAAQ,CAAC,CAAC,CAAC+X,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvE;;MAEA;MACA,IAAI,IAAI,CAACsB,QAAQ,EAAE;QACf,IAAMoC,gBAAgB,GAAI,IAAI,CAACf,aAAa,GAAG,IAAI,CAACF,SAAS,GAAI,GAAG;QACpE,IAAI,CAACnB,QAAQ,CAACva,KAAK,CAAC4c,KAAK,MAAAnd,MAAA,CAAMkd,gBAAgB,MAAG;;QAElD;QACA,IAAIA,gBAAgB,GAAG,EAAE,EAAE;UACvB,IAAI,CAACpC,QAAQ,CAACva,KAAK,CAAC6c,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD,CAAC,MAAM,IAAIF,gBAAgB,GAAG,EAAE,EAAE;UAC9B,IAAI,CAACpC,QAAQ,CAACva,KAAK,CAAC6c,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD,CAAC,MAAM;UACH,IAAI,CAACtC,QAAQ,CAACva,KAAK,CAAC6c,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD;MACJ;IACJ;;IAEA;AACJ;AACA;EAFI;IAAAhe,GAAA;IAAAC,KAAA,EAGA,SAAAid,wBAAwBA,CAAA,EAAG;MAAA,IAAAhL,MAAA;MACvB,IAAI,CAAC,IAAI,CAACqK,kBAAkB,IAAI,CAAC,IAAI,CAACjB,SAAS,CAAClc,MAAM,EAAE;MAExD,IAAI,CAACmd,kBAAkB,CAACzW,SAAS,GAAG,EAAE;MAEtC,IAAI,CAACwV,SAAS,CAACrY,OAAO,CAAC,UAACgb,CAAC,EAAE5Y,KAAK,EAAK;QACjC,IAAM6Y,SAAS,GAAGhe,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC/C8d,SAAS,CAAC5d,SAAS,GAAG,0BAA0B;QAChD4d,SAAS,CAAC3Y,OAAO,CAACF,KAAK,GAAGA,KAAK;QAC/B6Y,SAAS,CAAC/a,gBAAgB,CAAC,OAAO,EAAE;UAAA,OAAM+O,MAAI,CAACiM,YAAY,CAAC9Y,KAAK,CAAC;QAAA,EAAC;QAEnE6M,MAAI,CAACqK,kBAAkB,CAAC7b,WAAW,CAACwd,SAAS,CAAC;MAClD,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAle,GAAA;IAAAC,KAAA,EAGA,SAAAme,wBAAwBA,CAAA,EAAG;MAAA,IAAAtE,MAAA;MACvB,IAAI,CAAC,IAAI,CAACyC,kBAAkB,EAAE;MAE9B,IAAM8B,UAAU,GAAG,IAAI,CAAC9B,kBAAkB,CAACzZ,gBAAgB,CAAC,2BAA2B,CAAC;MAExFub,UAAU,CAACpb,OAAO,CAAC,UAACib,SAAS,EAAE7Y,KAAK,EAAK;QAAA,IAAAiZ,qBAAA;QACrC;QACAJ,SAAS,CAAC3d,SAAS,CAACgD,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC;;QAE/D;QACA,IAAI8B,KAAK,KAAKyU,MAAI,CAAC0B,oBAAoB,EAAE;UACrC0C,SAAS,CAAC3d,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QACtC;QAEA,IAAMgJ,UAAU,IAAA8U,qBAAA,GAAGxE,MAAI,CAACwB,SAAS,CAACjW,KAAK,CAAC,cAAAiZ,qBAAA,uBAArBA,qBAAA,CAAuBje,EAAE;QAC5C,IAAImJ,UAAU,IAAIsQ,MAAI,CAACnV,WAAW,CAAC6E,UAAU,CAAC,EAAE;UAC5C0U,SAAS,CAAC3d,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACvC,CAAC,MAAM;UACH0d,SAAS,CAAC3d,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAR,GAAA;IAAAC,KAAA,EAGA,SAAAyd,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACpC,SAAS,CAAClc,MAAM,EAAE;MAE5B,IAAMkF,QAAQ,GAAG,IAAI,CAACgX,SAAS,CAAC,IAAI,CAACE,oBAAoB,CAAC;MAC1D,IAAI,CAAClX,QAAQ,EAAE;;MAEf;MACA,IAAI,IAAI,CAACkY,sBAAsB,EAAE;QAC7B,IAAI,CAACA,sBAAsB,CAACzb,WAAW,GAAG,CAAC,IAAI,CAACya,oBAAoB,GAAG,CAAC,EAAEnZ,QAAQ,CAAC,CAAC;MACxF;MAEA,IAAI,IAAI,CAACoa,qBAAqB,EAAE;QAC5B,IAAI,CAACA,qBAAqB,CAAC1b,WAAW,GAAG,IAAI,CAACua,SAAS,CAAClc,MAAM,CAACiD,QAAQ,CAAC,CAAC;MAC7E;;MAEA;MACA,IAAMkc,OAAO,GAAG,IAAI,CAAChD,QAAQ,CAACzT,IAAI,CAAC,UAAA0W,CAAC;QAAA,OAAIA,CAAC,CAACne,EAAE,KAAKiE,QAAQ,CAACma,UAAU;MAAA,EAAC;;MAErE;MACA,IAAIF,OAAO,EAAE;QACT,IAAI,IAAI,CAACvC,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACjb,WAAW,GAAGwd,OAAO,CAACG,KAAK,IAAI,EAAE;QACvD;QAEA,IAAI,IAAI,CAACzC,mBAAmB,EAAE;UAC1B,IAAI,CAACA,mBAAmB,CAACnW,SAAS,GAAGyY,OAAO,CAACI,YAAY,IAAI,EAAE;QACnE;MACJ;;MAEA;MACA,IAAI,IAAI,CAACzC,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAACnb,WAAW,eAAAH,MAAA,CAAe,IAAI,CAAC4a,oBAAoB,GAAG,CAAC,CAAE;MACjF;MAEA,IAAI,IAAI,CAACW,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACrW,SAAS,GAAGxB,QAAQ,CAACsa,OAAO,IAAI,EAAE;MACxD;;MAEA;MACA,IAAI,CAACC,qBAAqB,CAACva,QAAQ,CAAC;;MAEpC;MACA,IAAI,CAACwa,uBAAuB,CAAC,CAAC;;MAE9B;MACA,IAAI,CAACV,wBAAwB,CAAC,CAAC;IACnC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAApe,GAAA;IAAAC,KAAA,EAKA,SAAA4e,qBAAqBA,CAACva,QAAQ,EAAE;MAC5B,IAAI,CAAC,IAAI,CAAC8X,eAAe,IAAI,CAAC9X,QAAQ,EAAE;MAExC,IAAI,CAAC8X,eAAe,CAACtW,SAAS,GAAG,EAAE;MAEnC,IAAI;QACA;QACA,IAAMiZ,QAAQ,GAAGnV,0EAAuB,CAACC,cAAc,CACnDvF,QAAQ,EACR,IAAI,CAAC8X,eAAe,EACpB;UACIjW,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BxB,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BuE,gBAAgB,EAAE,IAAI,CAAC8V,UAAU,CAAC3b,IAAI,CAAC,IAAI;QAC/C,CACJ,CAAC;;QAED;QACA0b,QAAQ,CAAC5a,MAAM,CAAC,CAAC;MACrB,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACZD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;QAEjD;QACA,IAAI,CAACgd,sBAAsB,CAAC3a,QAAQ,CAAC;MACzC;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAtE,GAAA;IAAAC,KAAA,EAKA,SAAAgf,sBAAsBA,CAAC3a,QAAQ,EAAE;MAC7B,IAAM4a,eAAe,GAAGhf,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACrD8e,eAAe,CAAC5e,SAAS,GAAG,yBAAyB;MACrD4e,eAAe,CAACpZ,SAAS,kGAAAlF,MAAA,CAEK0D,QAAQ,CAACoF,IAAI,IAAI,SAAS,wEAEvD;MAED,IAAI,CAAC0S,eAAe,CAAC1b,WAAW,CAACwe,eAAe,CAAC;IACrD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAlf,GAAA;IAAAC,KAAA,EAMA,SAAA+e,UAAUA,CAACxV,UAAU,EAAEnC,MAAM,EAAE;MAC3B,IAAI,CAAC1C,WAAW,CAAC6E,UAAU,CAAC,GAAGnC,MAAM;MACrC,IAAI,CAAC+W,wBAAwB,CAAC,CAAC;IACnC;;IAEA;AACJ;AACA;EAFI;IAAApe,GAAA;IAAAC,KAAA,EAGA,SAAA6e,uBAAuBA,CAAA,EAAG;MACtB;MACA,IAAI,IAAI,CAACzC,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC1V,QAAQ,GAAG,IAAI,CAAC6U,oBAAoB,KAAK,CAAC;MAC9D;;MAEA;MACA,IAAI,IAAI,CAACc,UAAU,EAAE;QACjB,IAAM6C,cAAc,GAAG,IAAI,CAAC3D,oBAAoB,KAAK,IAAI,CAACF,SAAS,CAAClc,MAAM,GAAG,CAAC;QAC9E,IAAI,CAACkd,UAAU,CAACnb,KAAK,CAACC,OAAO,GAAG+d,cAAc,GAAG,MAAM,GAAG,cAAc;MAC5E;;MAEA;MACA;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAnf,GAAA;IAAAC,KAAA,EAKA,SAAAke,YAAYA,CAAC9Y,KAAK,EAAE;MAChB,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACiW,SAAS,CAAClc,MAAM,EAAE;MAEjD,IAAI,CAACoc,oBAAoB,GAAGnW,KAAK;MACjC,IAAI,CAACqY,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAA1d,GAAA;IAAAC,KAAA,EAGA,SAAAod,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC7B,oBAAoB,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACA,oBAAoB,EAAE;QAC3B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;MAC9B;IACJ;;IAEA;AACJ;AACA;EAFI;IAAA1d,GAAA;IAAAC,KAAA,EAGA,SAAAqd,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAAC9B,oBAAoB,GAAG,IAAI,CAACF,SAAS,CAAClc,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAACoc,oBAAoB,EAAE;QAC3B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;MAC9B;IACJ;;IAEA;AACJ;AACA;EAFI;IAAA1d,GAAA;IAAAC,KAAA,EAGA,SAAAsd,iBAAiBA,CAAA,EAAG;MAChB;MACA,IAAM6B,aAAa,GAAGjU,MAAM,CAACkU,IAAI,CAAC,IAAI,CAAC1a,WAAW,CAAC,CAACvF,MAAM;MAC1D,IAAMkgB,eAAe,GAAG,IAAI,CAAChE,SAAS,CAAClc,MAAM,GAAGggB,aAAa;MAE7D,IAAItH,OAAO,GAAG,4CAA4C;MAE1D,IAAIwH,eAAe,GAAG,CAAC,EAAE;QACrBxH,OAAO,eAAAlX,MAAA,CAAe0e,eAAe,0BAAA1e,MAAA,CAAuB0e,eAAe,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,iDAA8C;MAC5I;MAEA,IAAIC,OAAO,CAACzH,OAAO,CAAC,EAAE;QAClB,IAAI,CAAC+F,UAAU,CAAC,CAAC;MACrB;IACJ;;IAEA;AACJ;AACA;EAFI;IAAA7d,GAAA;IAAAC,KAAA,EAGA,SAAA4d,UAAUA,CAAA,EAAG;MAAA,IAAA2B,MAAA;MACT;MACA,IAAI,IAAI,CAACpR,aAAa,EAAE;QACpBiM,aAAa,CAAC,IAAI,CAACjM,aAAa,CAAC;MACrC;MAEA,IAAI,CAACqN,YAAY,GAAG,KAAK;;MAEzB;MACA,IAAI,CAAC7b,MAAM,CAACqB,IAAI,CAAC,yBAAyB,CAAC;;MAE3C;MACA,IAAM8P,IAAI,GAAG;QACT0O,OAAO,EAAE,IAAI,CAACrE,QAAQ,CAAC/a,EAAE;QACzBqf,UAAU,EAAE,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACE,aAAa;QAC/C4C,OAAO,EAAE,IAAI,CAAChb;MAClB,CAAC;;MAED;MACAuW,6DAAW,CAAC2C,UAAU,CAAC,IAAI,CAACzC,QAAQ,CAAC/a,EAAE,EAAE,IAAI,CAACsE,WAAW,EAAE,IAAI,CAACkY,SAAS,GAAG,IAAI,CAACE,aAAa,CAAC,CAC1F9P,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACdiZ,MAAI,CAACI,WAAW,CAACrZ,QAAQ,CAAC;MAC9B,CAAC,CAAC,SACI,CAAC,UAAAtE,KAAK,EAAI;QACZD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9Cmb,KAAK,CAAC,+CAA+C,CAAC;MAC1D,CAAC,CAAC,WACM,CAAC,YAAM;QACXoC,MAAI,CAAC5f,MAAM,CAACoB,IAAI,CAAC,CAAC;MACtB,CAAC,CAAC;IACV;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhB,GAAA;IAAAC,KAAA,EAKA,SAAA2f,WAAWA,CAACC,OAAO,EAAE;MACjB;MACA,IAAI,IAAI,CAAC/D,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAAC3a,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;;MAEA;MACA,IAAI,IAAI,CAACsb,gBAAgB,EAAE;QACvB,IAAI,CAACA,gBAAgB,CAACvb,KAAK,CAACC,OAAO,GAAG,OAAO;MACjD;;MAEA;MACA,IAAM0e,iBAAiB,GAAG5f,QAAQ,CAACmB,aAAa,CAAC,wBAAwB,CAAC;MAC1E,IAAIye,iBAAiB,EAAE;QACnBA,iBAAiB,CAAC3e,KAAK,CAACC,OAAO,GAAG,MAAM;MAC5C;;MAEA;MACA,IAAI,IAAI,CAACub,cAAc,EAAE;QACrB,IAAM/C,KAAK,GAAGiG,OAAO,CAACjG,KAAK,IAAI,CAAC;QAChC,IAAMmG,WAAW,GAAGF,OAAO,CAACG,YAAY,IAAI,IAAI,CAAC1E,SAAS,CAAClc,MAAM;QACjE,IAAM6gB,UAAU,GAAG9d,IAAI,CAACmW,KAAK,CAAEsB,KAAK,GAAGmG,WAAW,GAAI,GAAG,CAAC;QAE1D,IAAIG,WAAW,GAAG,SAAS;QAC3B,IAAID,UAAU,IAAI,EAAE,EAAE;UAClBC,WAAW,GAAG,WAAW;QAC7B,CAAC,MAAM,IAAID,UAAU,IAAI,EAAE,EAAE;UACzBC,WAAW,GAAG,MAAM;QACxB,CAAC,MAAM,IAAID,UAAU,GAAG,EAAE,EAAE;UACxBC,WAAW,GAAG,MAAM;QACxB;QAEA,IAAI,CAACvD,cAAc,CAAC7W,SAAS,iDAAAlF,MAAA,CACCsf,WAAW,iEAAAtf,MAAA,CACCgZ,KAAK,OAAAhZ,MAAA,CAAImf,WAAW,wEAAAnf,MAAA,CAChBqf,UAAU,omBAAArf,MAAA,CAetC,IAAI,CAACuf,mBAAmB,CAACN,OAAO,CAACO,cAAc,IAAI,EAAE,CAAC,gOAAAxf,MAAA,CAO3Dif,OAAO,CAAC5G,QAAQ,IAAI,wBAAwB,+CAExD;MACL;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAjZ,GAAA;IAAAC,KAAA,EAMA,SAAAkgB,mBAAmBA,CAACE,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAACjhB,MAAM,EAAE;QACzC,OAAO,2DAA2D;MACtE;MAEA,OAAOihB,aAAa,CAACjY,GAAG,CAAC,UAAAmW,OAAO,EAAI;QAChC,IAAM0B,UAAU,GAAG9d,IAAI,CAACmW,KAAK,CAAEiG,OAAO,CAAC3E,KAAK,GAAG2E,OAAO,CAAC+B,KAAK,GAAI,GAAG,CAAC;QACpE,0DAAA1f,MAAA,CAEc2d,OAAO,CAACG,KAAK,qCAAA9d,MAAA,CACb2d,OAAO,CAAC3E,KAAK,qCAAAhZ,MAAA,CACb2d,OAAO,CAAC+B,KAAK,qCAAA1f,MAAA,CACbqf,UAAU;MAG5B,CAAC,CAAC,CAAC1G,IAAI,CAAC,EAAE,CAAC;IACf;;IAEA;AACJ;AACA;EAFI;IAAAvZ,GAAA;IAAAC,KAAA,EAGA,SAAAud,eAAeA,CAAA,EAAG;MACd,IAAI,CAACrX,YAAY,GAAG,IAAI;;MAExB;MACA,IAAI,IAAI,CAAC2V,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAAC3a,KAAK,CAACC,OAAO,GAAG,OAAO;MAClD;;MAEA;MACA,IAAM0e,iBAAiB,GAAG5f,QAAQ,CAACmB,aAAa,CAAC,wBAAwB,CAAC;MAC1E,IAAIye,iBAAiB,EAAE;QACnBA,iBAAiB,CAAC3e,KAAK,CAACC,OAAO,GAAG,OAAO;;QAEzC;QACA,IAAI,IAAI,CAACsF,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACvF,KAAK,CAACC,OAAO,GAAG,MAAM;QAC5C;;QAEA;QACA,IAAI,IAAI,CAACkb,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,CAACnb,KAAK,CAACC,OAAO,GAAG,cAAc;QAClD;MACJ;;MAEA;MACA,IAAI,IAAI,CAACsb,gBAAgB,EAAE;QACvB,IAAI,CAACA,gBAAgB,CAACvb,KAAK,CAACC,OAAO,GAAG,MAAM;MAChD;;MAEA;MACA,IAAI,CAACoa,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;IAC9B;EAAC;AAAA,KAGL;AACAxd,QAAQ,CAACiD,gBAAgB,CAAC,kBAAkB,EAAE,YAAM;EAChD,IAAMod,iBAAiB,GAAGrgB,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;EACtE,IAAIogB,iBAAiB,EAAE;IACnB,IAAMC,cAAc,GAAG,IAAIrF,cAAc,CAACoF,iBAAiB,CAAC;IAC5DC,cAAc,CAAC1gB,IAAI,CAAC,CAAC;EACzB;AACJ,CAAC,CAAC;AAEF,iEAAeqb,cAAc,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1mB7B;AACA;AACA;AACA;AACA;AACA;AALA,IAMMsF,WAAW;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,YAAA,EAA0B;IAAA,IAAdvhB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAmhB,WAAA;IACpB;IACA,IAAMC,UAAU,GAAGle,MAAM,CAACme,iBAAiB,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACC,OAAO,GAAG1hB,OAAO,CAAC0hB,OAAO,IAAIF,UAAU,CAACE,OAAO,IAAI,0BAA0B;IAClF,IAAI,CAACC,KAAK,GAAG3hB,OAAO,CAAC2hB,KAAK,IAAIH,UAAU,CAACG,KAAK,IAAI,EAAE;IACpD,IAAI,CAACC,UAAU,GAAG5hB,OAAO,CAAC4hB,UAAU,IAAI,OAAO;IAC/C,IAAI,CAACC,OAAO,GAAG7hB,OAAO,CAAC6hB,OAAO,IAAI,IAAI;;IAEtC;IACA,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACf5e,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEA,IAAI,CAAC,IAAI,CAAC4e,KAAK,EAAE;MACb7e,OAAO,CAAC2M,IAAI,CAAC,uEAAuE,CAAC;IACzF;;IAEA;IACA,IAAI,CAACqS,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI,OAAAlhB,YAAA,CAAA0gB,WAAA;IAAAzgB,GAAA;IAAAC,KAAA,EAQA,SAAAihB,GAAGA,CAACC,MAAM,EAA6B;MAAA,IAA3B/J,MAAM,GAAAjY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACjC,IAAMiiB,aAAa,GAAAze,aAAA,CAAA0e,eAAA;QACfF,MAAM,EAANA;MAAM,GACL,IAAI,CAACL,UAAU,EAAG,IAAI,CAACD,KAAK,GAC1BzJ,MAAM,CACZ;MAED,IAAMkK,WAAW,GAAG,IAAIC,eAAe,CAACH,aAAa,CAAC,CAAC/e,QAAQ,CAAC,CAAC;MACjE,IAAMmf,GAAG,MAAA5gB,MAAA,CAAM,IAAI,CAACggB,OAAO,OAAAhgB,MAAA,CAAI0gB,WAAW,CAAE;MAE5C,OAAO,IAAI,CAACG,YAAY,CAACD,GAAG,EAAA7e,aAAA;QACxB+e,MAAM,EAAE;MAAK,GACVxiB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAA0hB,IAAIA,CAACR,MAAM,EAA2B;MAAA,IAAzBpQ,IAAI,GAAA5R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAChC,IAAMyiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEX,MAAM,CAAC;MACjCS,QAAQ,CAACE,MAAM,CAAC,IAAI,CAAChB,UAAU,EAAE,IAAI,CAACD,KAAK,CAAC;;MAE5C;MACA1V,MAAM,CAAC4W,OAAO,CAAChR,IAAI,CAAC,CAAC9N,OAAO,CAAC,UAAAuF,IAAA,EAAkB;QAAA,IAAAmK,KAAA,GAAAqP,cAAA,CAAAxZ,IAAA;UAAhBxI,GAAG,GAAA2S,KAAA;UAAE1S,KAAK,GAAA0S,KAAA;QACrC;QACA,IAAI1S,KAAK,YAAY2R,IAAI,EAAE;UACvBgQ,QAAQ,CAACE,MAAM,CAAC9hB,GAAG,EAAEC,KAAK,EAAEA,KAAK,CAACgiB,IAAI,OAAArhB,MAAA,CAAOZ,GAAG,UAAO,CAAC;QAC5D;QACA;QAAA,KACK,IAAIyN,OAAA,CAAOxN,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UAClD2hB,QAAQ,CAACE,MAAM,CAAC9hB,GAAG,EAAEuE,IAAI,CAAC2d,SAAS,CAACjiB,KAAK,CAAC,CAAC;QAC/C,CAAC,MAAM;UACH2hB,QAAQ,CAACE,MAAM,CAAC9hB,GAAG,EAAEC,KAAK,CAAC;QAC/B;MACJ,CAAC,CAAC;MAEF,OAAO,IAAI,CAACwhB,YAAY,CAAC,IAAI,CAACb,OAAO,EAAAje,aAAA;QACjC+e,MAAM,EAAE,MAAM;QACdjhB,IAAI,EAAEmhB;MAAQ,GACX1iB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAkiB,QAAQA,CAAChB,MAAM,EAA2B;MAAA,IAAzBpQ,IAAI,GAAA5R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACpC,IAAMijB,WAAW,GAAAzf,aAAA,CAAA0e,eAAA;QACbF,MAAM,EAANA;MAAM,GACL,IAAI,CAACL,UAAU,EAAG,IAAI,CAACD,KAAK,GAC1B9P,IAAI,CACV;MAED,OAAO,IAAI,CAAC0Q,YAAY,CAAC,IAAI,CAACb,OAAO,EAAAje,aAAA;QACjC+e,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACD5hB,IAAI,EAAE8D,IAAI,CAAC2d,SAAS,CAACE,WAAW;MAAC,GAC9BljB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAwhB,YAAYA,CAACD,GAAG,EAAEtiB,OAAO,EAAE;MAAA,IAAAwC,KAAA;MACvB;MACA,IAAM4gB,SAAS,GAAGngB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACkgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE7D;MACA,IAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,IAAQC,MAAM,GAAKF,UAAU,CAArBE,MAAM;;MAEd;MACA,IAAMC,OAAO,GAAGzjB,OAAO,CAACyjB,OAAO,IAAI,KAAK,CAAC,CAAC;MAC1C,IAAMC,SAAS,GAAG/gB,UAAU,CAAC,YAAM;QAC/B2gB,UAAU,CAACK,KAAK,CAAC,CAAC;MACtB,CAAC,EAAEF,OAAO,CAAC;;MAEX;MACA,IAAI,CAAC3B,eAAe,CAAC8B,GAAG,CAACR,SAAS,EAAE;QAAEE,UAAU,EAAVA;MAAW,CAAC,CAAC;;MAEnD;MACA,IAAM5iB,MAAM,GAAGV,OAAO,CAACU,MAAM,IAAK4C,MAAM,CAACugB,WAAW,IAAI,IAAK;MAC7D,IAAMC,UAAU,GAAG9jB,OAAO,CAAC8jB,UAAU,KAAK,KAAK;MAE/C,IAAIpjB,MAAM,IAAIojB,UAAU,EAAE;QACtBpjB,MAAM,CAACqB,IAAI,CAAC/B,OAAO,CAAC+jB,UAAU,IAAI,YAAY,CAAC;MACnD;;MAEA;MACA,OAAOC,KAAK,CAAC1B,GAAG,EAAA7e,aAAA,CAAAA,aAAA,KACTzD,OAAO;QACVwjB,MAAM,EAANA,MAAM;QACNS,WAAW,EAAE;MAAa,EAC7B,CAAC,CACDlW,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACd;QACA6c,YAAY,CAACR,SAAS,CAAC;;QAEvB;QACA,IAAI,CAACrc,QAAQ,CAAC8c,EAAE,EAAE;UACd,MAAM,IAAIja,KAAK,eAAAxI,MAAA,CAAe2F,QAAQ,CAAC+c,MAAM,QAAA1iB,MAAA,CAAK2F,QAAQ,CAACgd,UAAU,CAAE,CAAC;QAC5E;;QAEA;QACA,IAAMC,WAAW,GAAGjd,QAAQ,CAAC8b,OAAO,CAACnB,GAAG,CAAC,cAAc,CAAC;QACxD,IAAIsC,WAAW,IAAIA,WAAW,CAACtZ,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACzD,OAAO3D,QAAQ,CAACkd,IAAI,CAAC,CAAC;QAC1B;QAEA,OAAOld,QAAQ,CAAC/G,IAAI,CAAC,CAAC;MAC1B,CAAC,CAAC,CACDyN,IAAI,CAAC,UAAA8D,IAAI,EAAI;QACV;QACA,IAAItD,OAAA,CAAOsD,IAAI,MAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;UAC3C;UACA,IAAIA,IAAI,CAAC2S,OAAO,KAAK,KAAK,EAAE;YACxB,MAAM,IAAIta,KAAK,CAAC2H,IAAI,CAACA,IAAI,IAAI,eAAe,CAAC;UACjD;;UAEA;UACA,OAAOA,IAAI,CAACA,IAAI,KAAK1R,SAAS,GAAG0R,IAAI,CAACA,IAAI,GAAGA,IAAI;QACrD;QAEA,OAAOA,IAAI;MACf,CAAC,CAAC,SACI,CAAC,UAAA9O,KAAK,EAAI;QACZ;QACA,IAAIA,KAAK,CAACggB,IAAI,KAAK,YAAY,EAAE;UAC7B,MAAM,IAAI7Y,KAAK,CAAC,mBAAmB,CAAC;QACxC;;QAEA;QACA,IAAI1H,KAAI,CAACqf,OAAO,EAAE;UACdrf,KAAI,CAACqf,OAAO,CAAC9e,KAAK,CAAC;QACvB;QAEA,MAAMA,KAAK;MACf,CAAC,CAAC,WACM,CAAC,YAAM;QACX;QACAP,KAAI,CAACsf,eAAe,UAAO,CAACsB,SAAS,CAAC;;QAEtC;QACA,IAAI1iB,MAAM,IAAIojB,UAAU,EAAE;UACtB;UACA,IAAMW,uBAAuB,GAAG/b,KAAK,CAACC,IAAI,CAACnG,KAAI,CAACsf,eAAe,CAAC4C,MAAM,CAAC,CAAC,CAAC,CACpEC,IAAI,CAAC,UAAAC,GAAG;YAAA,OAAIA,GAAG,CAAClkB,MAAM,KAAKA,MAAM;UAAA,EAAC;UAEvC,IAAI,CAAC+jB,uBAAuB,EAAE;YAC1B/jB,MAAM,CAACoB,IAAI,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAA8jB,SAASA,CAAA,EAAG;MACR,IAAI,CAAC/C,eAAe,CAAC/d,OAAO,CAAC,UAAA+gB,OAAO,EAAI;QACpCA,OAAO,CAACxB,UAAU,CAACK,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAI,CAAC7B,eAAe,CAACiD,KAAK,CAAC,CAAC;IAChC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAjkB,GAAA;IAAAC,KAAA,EAKA,SAAAikB,WAAWA,CAACrD,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA7gB,GAAA;IAAAC,KAAA,EAMA,SAAOmL,MAAMA,CAAA,EAAe;MAAA,IAAdlM,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtB,OAAO,IAAIshB,WAAW,CAACvhB,OAAO,CAAC;IACnC;EAAC;AAAA,KAGL;AACA,IAAMilB,WAAW,GAAG,IAAI1D,WAAW,CAAC,CAAC;AAErC,iEAAe0D,WAAW,EAAC;;AAE3B;;;;;;;;;;;;;;;;;0BClQA,uKAAA1f,CAAA,EAAA+F,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAzC,EAAAoC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAxC,CAAA,QAAA0C,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAxC,CAAA,EAAA0C,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAW,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAZ,CAAA,KAAAe,CAAA,EAAAjH,CAAA,EAAAkH,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAvI,IAAA,CAAAoB,CAAA,MAAAmH,CAAA,WAAAA,EAAApB,CAAA,EAAAC,CAAA,WAAApC,CAAA,GAAAmC,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAAzG,CAAA,EAAAgH,CAAA,CAAAd,CAAA,GAAAF,CAAA,EAAAkB,CAAA,gBAAAC,EAAAnB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAgB,CAAA,IAAAF,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAAnM,MAAA,EAAAoL,CAAA,UAAAK,CAAA,EAAAxC,CAAA,GAAAkD,CAAA,CAAAf,CAAA,GAAAoB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAM,CAAA,GAAAxD,CAAA,KAAAoC,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAA7C,CAAA,EAAA0C,CAAA,GAAA1C,CAAA,YAAA0C,CAAA,WAAA1C,CAAA,MAAAA,CAAA,MAAA5D,CAAA,IAAA4D,CAAA,OAAAuD,CAAA,MAAAf,CAAA,GAAAJ,CAAA,QAAAmB,CAAA,GAAAvD,CAAA,QAAA0C,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAf,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAtC,CAAA,OAAAuD,CAAA,GAAAC,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAApC,CAAA,MAAAsC,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAAxD,CAAA,MAAAoC,CAAA,EAAApC,CAAA,MAAAsC,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAkB,CAAA,QAAAH,CAAA,OAAAb,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAN,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAAtG,CAAA,GAAAyG,CAAA,MAAAM,CAAA,KAAAnD,CAAA,KAAA0C,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAd,CAAA,QAAAiB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAd,CAAA,GAAAO,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAjD,CAAA,QAAA0C,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAnC,CAAA,CAAAwC,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,EAAA6C,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAAvK,KAAA,EAAA8K,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAnC,CAAA,eAAAmC,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,GAAA0C,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAA1C,CAAA,GAAA5D,CAAA,cAAA+F,CAAA,IAAAgB,CAAA,GAAAC,CAAA,CAAAd,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAc,CAAA,OAAAE,CAAA,kBAAAnB,CAAA,IAAAnC,CAAA,GAAA5D,CAAA,EAAAsG,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAArL,KAAA,EAAAuK,CAAA,EAAAwB,IAAA,EAAAR,CAAA,SAAAf,CAAA,EAAAI,CAAA,EAAAxC,CAAA,QAAA6C,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAgB,kBAAA,cAAAC,2BAAA,KAAA1B,CAAA,GAAAW,MAAA,CAAAgB,cAAA,MAAApB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAgB,0BAAA,CAAAlB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAA7G,CAAA,WAAA0G,MAAA,CAAAiB,cAAA,GAAAjB,MAAA,CAAAiB,cAAA,CAAA3H,CAAA,EAAAyH,0BAAA,KAAAzH,CAAA,CAAA4H,SAAA,GAAAH,0BAAA,EAAAb,mBAAA,CAAA5G,CAAA,EAAAoG,CAAA,yBAAApG,CAAA,CAAAuG,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAzG,CAAA,WAAAwH,iBAAA,CAAAjB,SAAA,GAAAkB,0BAAA,EAAAb,mBAAA,CAAAH,CAAA,iBAAAgB,0BAAA,GAAAb,mBAAA,CAAAa,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAjB,mBAAA,CAAAa,0BAAA,EAAArB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAqB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAnE,CAAA,EAAAoE,CAAA,EAAAnB,CAAA;AAAA,SAAAD,oBAAA5G,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAnC,CAAA,GAAA8C,MAAA,CAAAuB,cAAA,QAAArE,CAAA,uBAAA5D,CAAA,IAAA4D,CAAA,QAAAgD,mBAAA,YAAAsB,mBAAAlI,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAApC,CAAA,GAAAA,CAAA,CAAA5D,CAAA,EAAAgG,CAAA,IAAAxK,KAAA,EAAA0K,CAAA,EAAAiC,UAAA,GAAApC,CAAA,EAAAqC,YAAA,GAAArC,CAAA,EAAAsC,QAAA,GAAAtC,CAAA,MAAA/F,CAAA,CAAAgG,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAA5G,CAAA,EAAAgG,CAAA,YAAAhG,CAAA,gBAAAsI,OAAA,CAAAtC,CAAA,EAAAE,CAAA,EAAAlG,CAAA,UAAAoG,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAA5G,CAAA,EAAAgG,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAwC,mBAAArC,CAAA,EAAAH,CAAA,EAAA/F,CAAA,EAAAgG,CAAA,EAAAI,CAAA,EAAAc,CAAA,EAAAZ,CAAA,cAAA1C,CAAA,GAAAsC,CAAA,CAAAgB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAA7C,CAAA,CAAApI,KAAA,WAAA0K,CAAA,gBAAAlG,CAAA,CAAAkG,CAAA,KAAAtC,CAAA,CAAA2D,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAAvJ,OAAA,CAAAC,OAAA,CAAAsJ,CAAA,EAAA+B,IAAA,CAAAxC,CAAA,EAAAI,CAAA;AAAA,SAAAqC,kBAAAvC,CAAA,6BAAAH,CAAA,SAAA/F,CAAA,GAAAtF,SAAA,aAAAwC,OAAA,WAAA8I,CAAA,EAAAI,CAAA,QAAAc,CAAA,GAAAhB,CAAA,CAAAwC,KAAA,CAAA3C,CAAA,EAAA/F,CAAA,YAAA2I,MAAAzC,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,UAAA1C,CAAA,cAAA0C,OAAA1C,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,WAAA1C,CAAA,KAAAyC,KAAA;AAAA,SAAA9N,gBAAAqM,CAAA,EAAAhB,CAAA,UAAAgB,CAAA,YAAAhB,CAAA,aAAAmB,SAAA;AAAA,SAAAwB,kBAAA7I,CAAA,EAAAgG,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAArL,MAAA,EAAAoL,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAA+B,UAAA,GAAA/B,CAAA,CAAA+B,UAAA,QAAA/B,CAAA,CAAAgC,YAAA,kBAAAhC,CAAA,KAAAA,CAAA,CAAAiC,QAAA,QAAA3B,MAAA,CAAAuB,cAAA,CAAAjI,CAAA,EAAA8I,cAAA,CAAA1C,CAAA,CAAA7K,GAAA,GAAA6K,CAAA;AAAA,SAAA9K,aAAA0E,CAAA,EAAAgG,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA6C,iBAAA,CAAA7I,CAAA,CAAAuG,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAA8C,iBAAA,CAAA7I,CAAA,EAAA+F,CAAA,GAAAW,MAAA,CAAAuB,cAAA,CAAAjI,CAAA,iBAAAqI,QAAA,SAAArI,CAAA;AAAA,SAAA8I,eAAA/C,CAAA,QAAAnC,CAAA,GAAAmF,YAAA,CAAAhD,CAAA,gCAAAiD,OAAA,CAAApF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAmF,aAAAhD,CAAA,EAAAC,CAAA,oBAAAgD,OAAA,CAAAjD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAA/F,CAAA,GAAA+F,CAAA,CAAAE,MAAA,CAAAgD,WAAA,kBAAAjJ,CAAA,QAAA4D,CAAA,GAAA5D,CAAA,CAAAsH,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAAgD,OAAA,CAAApF,CAAA,UAAAA,CAAA,YAAAyD,SAAA,yEAAArB,CAAA,GAAAkD,MAAA,GAAAC,MAAA,EAAApD,CAAA;AADA;AACA;AACA;AACA;AAC2C;AAAA,IAErCqD,oBAAoB;EACtB,SAAAA,qBAAA,EAAc;IAAAvO,eAAA,OAAAuO,oBAAA;IACV,IAAI,CAACsW,WAAW,GAAGA,uDAAW;EAClC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EALI,OAAApkB,YAAA,CAAA8N,oBAAA;IAAA7N,GAAA;IAAAC,KAAA;MAAA,IAAAmkB,eAAA,GAAAlX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAMA,SAAAyC,QAAqByC,SAAS;QAAA,IAAAyF,MAAA;UAAArG,IAAA;UAAAsT,KAAA,GAAAllB,SAAA;UAAAiQ,EAAA;QAAA,OAAA7C,YAAA,GAAAC,CAAA,WAAA6C,QAAA;UAAA,kBAAAA,QAAA,CAAA9D,CAAA,GAAA8D,QAAA,CAAA1E,CAAA;YAAA;cAAEyM,MAAM,GAAAiN,KAAA,CAAAjlB,MAAA,QAAAilB,KAAA,QAAAhlB,SAAA,GAAAglB,KAAA,MAAG,CAAC,CAAC;cAAAhV,QAAA,CAAA9D,CAAA;cAEnC;cACMwF,IAAI,GAAG;gBACT/K,KAAK,EAAE2L,SAAS;gBAChB8F,QAAQ,EAAEL,MAAM,CAACK,QAAQ,IAAI,EAAE;gBAC/BC,OAAO,EAAEN,MAAM,CAACM,OAAO,IAAI,EAAE;gBAC7BC,SAAS,EAAEP,MAAM,CAACO,SAAS,IAAI;cACnC,CAAC,EAED;cAAAtI,QAAA,CAAA1E,CAAA;cAAA,OACa,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,qBAAqB,EAAE5Q,IAAI,CAAC;YAAA;cAAA,OAAA1B,QAAA,CAAA1D,CAAA,IAAA0D,QAAA,CAAA3D,CAAA;YAAA;cAAA2D,QAAA,CAAA9D,CAAA;cAAA6D,EAAA,GAAAC,QAAA,CAAA3D,CAAA;cAG/D1J,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAAmN,EAAO,CAAC;cAAC,MAAAA,EAAA;YAAA;cAAA,OAAAC,QAAA,CAAA1D,CAAA;UAAA;QAAA,GAAAuD,OAAA;MAAA,CAGxD;MAAA,SAjBK0I,cAAcA,CAAAnE,EAAA;QAAA,OAAA2Q,eAAA,CAAAjX,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAdyY,cAAc;IAAA;IAmBpB;AACJ;AACA;AACA;IAHI;EAAA;IAAA5X,GAAA;IAAAC,KAAA;MAAA,IAAAqkB,UAAA,GAAApX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAIA,SAAAqD,SAAA;QAAA,IAAAO,GAAA;QAAA,OAAA9D,YAAA,GAAAC,CAAA,WAAAuD,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,CAAA,GAAAwE,SAAA,CAAApF,CAAA;YAAA;cAAAoF,SAAA,CAAAxE,CAAA;cAAAwE,SAAA,CAAApF,CAAA;cAAA,OAEqB,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,0BAA0B,CAAC;YAAA;cAAA,OAAA5R,SAAA,CAAApE,CAAA,IAAAoE,SAAA,CAAArE,CAAA;YAAA;cAAAqE,SAAA,CAAAxE,CAAA;cAAA8E,GAAA,GAAAN,SAAA,CAAArE,CAAA;cAE9D1J,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAAoO,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAAN,SAAA,CAAApE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA,CAGtD;MAAA,SAPKyU,SAASA,CAAA;QAAA,OAAAD,UAAA,CAAAnX,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAATolB,SAAS;IAAA;IASf;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAAvkB,GAAA;IAAAC,KAAA;MAAA,IAAAukB,cAAA,GAAAtX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAA0D,SAAoBsU,OAAO;QAAA,IAAAzR,GAAA;QAAA,OAAAzG,YAAA,GAAAC,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,CAAA,GAAA+E,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA+E,SAAA,CAAA3F,CAAA;cAAA,OAEN,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,iBAAiB,EAAE;gBAClDlK,QAAQ,EAAEgN;cACd,CAAC,CAAC;YAAA;cAAA,OAAAnU,SAAA,CAAA3E,CAAA,IAAA2E,SAAA,CAAA5E,CAAA;YAAA;cAAA4E,SAAA,CAAA/E,CAAA;cAAAyH,GAAA,GAAA1C,SAAA,CAAA5E,CAAA;cAEF1J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAA+Q,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAA1C,SAAA,CAAA3E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAG3D;MAAA,SATKuU,aAAaA,CAAApR,GAAA;QAAA,OAAAkR,cAAA,CAAArX,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAbulB,aAAa;IAAA;IAWnB;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAA1kB,GAAA;IAAAC,KAAA;MAAA,IAAA0kB,WAAA,GAAAzX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAAmG,SAAiBgS,UAAU;QAAA,IAAAvN,GAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAAyG,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,CAAA,GAAA0H,SAAA,CAAAtI,CAAA;YAAA;cAAAsI,SAAA,CAAA1H,CAAA;cAAA0H,SAAA,CAAAtI,CAAA;cAAA,OAEN,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,2BAA2B,EAAE;gBAC5DkD,WAAW,EAAEtgB,IAAI,CAAC2d,SAAS,CAAC0C,UAAU;cAC1C,CAAC,CAAC;YAAA;cAAA,OAAA3R,SAAA,CAAAtH,CAAA,IAAAsH,SAAA,CAAAvH,CAAA;YAAA;cAAAuH,SAAA,CAAA1H,CAAA;cAAA8L,GAAA,GAAApE,SAAA,CAAAvH,CAAA;cAEF1J,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAAoV,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAApE,SAAA,CAAAtH,CAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA,CAGpD;MAAA,SATKkS,UAAUA,CAAA9M,GAAA;QAAA,OAAA2M,WAAA,CAAAxX,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAV2lB,UAAU;IAAA;IAWhB;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAA9kB,GAAA;IAAAC,KAAA;MAAA,IAAA8kB,WAAA,GAAA7X,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAAwF,SAAA;QAAA,IAAA+S,OAAA;UAAAjU,IAAA;UAAAkU,MAAA,GAAA9lB,SAAA;UAAA2b,GAAA;QAAA,OAAAvO,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,CAAA,GAAA4G,SAAA,CAAAxH,CAAA;YAAA;cAAiBqa,OAAO,GAAAC,MAAA,CAAA7lB,MAAA,QAAA6lB,MAAA,QAAA5lB,SAAA,GAAA4lB,MAAA,MAAG,CAAC,CAAC;cAAA9S,SAAA,CAAA5G,CAAA;cAEfwF,IAAI,GAAG,CAAC,CAAC,EAEf;cACA,IAAIiU,OAAO,CAACvN,QAAQ,EAAE;gBAClB1G,IAAI,CAAC0G,QAAQ,GAAGuN,OAAO,CAACvN,QAAQ;cACpC;cACA,IAAIuN,OAAO,CAACE,KAAK,EAAE;gBACfnU,IAAI,CAACmU,KAAK,GAAGF,OAAO,CAACE,KAAK;cAC9B;cACA,IAAIF,OAAO,CAAChP,MAAM,EAAE;gBAChBjF,IAAI,CAACiF,MAAM,GAAGgP,OAAO,CAAChP,MAAM;cAChC;cAAC7D,SAAA,CAAAxH,CAAA;cAAA,OAEY,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,2BAA2B,EAAE5Q,IAAI,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAxG,CAAA,IAAAwG,SAAA,CAAAzG,CAAA;YAAA;cAAAyG,SAAA,CAAA5G,CAAA;cAAAuP,GAAA,GAAA3I,SAAA,CAAAzG,CAAA;cAErE1J,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAA6Y,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAA3I,SAAA,CAAAxG,CAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA,CAGvD;MAAA,SApBKkT,UAAUA,CAAA;QAAA,OAAAJ,WAAA,CAAA5X,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAVgmB,UAAU;IAAA;IAsBhB;AACJ;AACA;AACA;IAHI;EAAA;IAAAnlB,GAAA;IAAAC,KAAA;MAAA,IAAAmlB,eAAA,GAAAlY,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAIA,SAAA0K,SAAA3O,IAAA;QAAA,IAAA7C,MAAA,EAAA8U,OAAA,EAAA1J,IAAA,EAAAsU,GAAA;QAAA,OAAA9Y,YAAA,GAAAC,CAAA,WAAA8K,SAAA;UAAA,kBAAAA,SAAA,CAAA/L,CAAA,GAAA+L,SAAA,CAAA3M,CAAA;YAAA;cAAsBhF,MAAM,GAAA6C,IAAA,CAAN7C,MAAM,EAAE8U,OAAO,GAAAjS,IAAA,CAAPiS,OAAO;cAAAnD,SAAA,CAAA/L,CAAA;cAEzBwF,IAAI,GAAG;gBACP,QAAQ,EAAEpL,MAAM;gBAChB,SAAS,EAAE8U;cACf,CAAC;cAAAnD,SAAA,CAAA3M,CAAA;cAAA,OACY,IAAI,CAACwZ,WAAW,CAACxC,IAAI,CAAC,+BAA+B,EAAE5Q,IAAI,CAAC;YAAA;cAAA,OAAAuG,SAAA,CAAA3L,CAAA,IAAA2L,SAAA,CAAA5L,CAAA;YAAA;cAAA4L,SAAA,CAAA/L,CAAA;cAAA8Z,GAAA,GAAA/N,SAAA,CAAA5L,CAAA;cAEzE1J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAAojB,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAA/N,SAAA,CAAA3L,CAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA,CAG3D;MAAA,SAXK6D,cAAcA,CAAAC,GAAA;QAAA,OAAAmK,eAAA,CAAAjY,KAAA,OAAAhO,SAAA;MAAA;MAAA,OAAd6b,cAAc;IAAA;EAAA;AAAA,KAcxB;AACA,iEAAenN,oBAAoB,E;;;;;;;;;;;;;;;;;;;;;;;;AC/HnC;AACA;AACA;AACA;AACA;AACA;AALA,IAMMyX,WAAW;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,YAAA,EAA0B;IAAA,IAAdpmB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAgmB,WAAA;IACpB;IACA,IAAM5E,UAAU,GAAGle,MAAM,CAAC+iB,iBAAiB,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACC,OAAO,GAAGtmB,OAAO,CAACsmB,OAAO,IAAI9E,UAAU,CAAC8E,OAAO,IAAI,4BAA4B;IACpF,IAAI,CAAC3E,KAAK,GAAG3hB,OAAO,CAAC2hB,KAAK,IAAIH,UAAU,CAAC+E,SAAS,IAAI,EAAE;IACxD,IAAI,CAAC1E,OAAO,GAAG7hB,OAAO,CAAC6hB,OAAO,IAAI,IAAI;;IAEtC;IACA,IAAI,CAAC,IAAI,CAACyE,OAAO,EAAE;MACfxjB,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEA,IAAI,CAAC,IAAI,CAAC4e,KAAK,EAAE;MACb7e,OAAO,CAAC2M,IAAI,CAAC,uEAAuE,CAAC;IACzF;;IAEA;IACA,IAAI,CAACqS,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI,OAAAlhB,YAAA,CAAAulB,WAAA;IAAAtlB,GAAA;IAAAC,KAAA,EAQA,SAAAihB,GAAGA,CAACwE,QAAQ,EAA6B;MAAA,IAA3BtO,MAAM,GAAAjY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnC;MACA,IAAMmiB,WAAW,GAAGnW,MAAM,CAACkU,IAAI,CAACjI,MAAM,CAAC,CAAChY,MAAM,GACxC,GAAG,GAAG,IAAImiB,eAAe,CAACnK,MAAM,CAAC,CAAC/U,QAAQ,CAAC,CAAC,GAC5C,EAAE;MAER,IAAMmf,GAAG,MAAA5gB,MAAA,CAAM,IAAI,CAAC4kB,OAAO,EAAA5kB,MAAA,CAAG8kB,QAAQ,EAAA9kB,MAAA,CAAG0gB,WAAW,CAAE;MAEtD,OAAO,IAAI,CAACG,YAAY,CAACD,GAAG,EAAA7e,aAAA;QACxB+e,MAAM,EAAE;MAAK,GACVxiB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAA0hB,IAAIA,CAAC+D,QAAQ,EAA2B;MAAA,IAAzB3U,IAAI,GAAA5R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAClC,IAAMqiB,GAAG,MAAA5gB,MAAA,CAAM,IAAI,CAAC4kB,OAAO,EAAA5kB,MAAA,CAAG8kB,QAAQ,CAAE;MAExC,OAAO,IAAI,CAACjE,YAAY,CAACD,GAAG,EAAA7e,aAAA;QACxB+e,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACD5hB,IAAI,EAAE8D,IAAI,CAAC2d,SAAS,CAACnR,IAAI;MAAC,GACvB7R,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAwhB,YAAYA,CAACD,GAAG,EAAEtiB,OAAO,EAAE;MAAA,IAAAwC,KAAA;MACvB;MACA,IAAM4gB,SAAS,GAAGngB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACkgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE7D;MACA,IAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,IAAQC,MAAM,GAAKF,UAAU,CAArBE,MAAM;;MAEd;MACA,IAAMC,OAAO,GAAGzjB,OAAO,CAACyjB,OAAO,IAAI,KAAK,CAAC,CAAC;MAC1C,IAAMC,SAAS,GAAG/gB,UAAU,CAAC,YAAM;QAC/B2gB,UAAU,CAACK,KAAK,CAAC,CAAC;MACtB,CAAC,EAAEF,OAAO,CAAC;;MAEX;MACA,IAAI,CAAC3B,eAAe,CAAC8B,GAAG,CAACR,SAAS,EAAE;QAAEE,UAAU,EAAVA;MAAW,CAAC,CAAC;;MAEnD;MACA,IAAM5iB,MAAM,GAAGV,OAAO,CAACU,MAAM,IAAK4C,MAAM,CAACugB,WAAW,IAAI,IAAK;MAC7D,IAAMC,UAAU,GAAG9jB,OAAO,CAAC8jB,UAAU,KAAK,KAAK;MAE/C,IAAIpjB,MAAM,IAAIojB,UAAU,EAAE;QACtBpjB,MAAM,CAACqB,IAAI,CAAC/B,OAAO,CAAC+jB,UAAU,IAAI,YAAY,CAAC;MACnD;;MAEA;MACA,IAAMZ,OAAO,GAAGnjB,OAAO,CAACmjB,OAAO,IAAI,CAAC,CAAC;MACrCA,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAACxB,KAAK,CAAC,CAAC;;MAEpC;MACA,OAAOqC,KAAK,CAAC1B,GAAG,EAAA7e,aAAA,CAAAA,aAAA,KACTzD,OAAO;QACVmjB,OAAO,EAAPA,OAAO;QACPK,MAAM,EAANA,MAAM;QACNS,WAAW,EAAE;MAAa,EAC7B,CAAC,CACDlW,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACd;QACA6c,YAAY,CAACR,SAAS,CAAC;;QAEvB;QACA,IAAI,CAACrc,QAAQ,CAAC8c,EAAE,EAAE;UACd,MAAM,IAAIja,KAAK,eAAAxI,MAAA,CAAe2F,QAAQ,CAAC+c,MAAM,QAAA1iB,MAAA,CAAK2F,QAAQ,CAACgd,UAAU,CAAE,CAAC;QAC5E;;QAEA;QACA,IAAMC,WAAW,GAAGjd,QAAQ,CAAC8b,OAAO,CAACnB,GAAG,CAAC,cAAc,CAAC;QACxD,IAAIsC,WAAW,IAAIA,WAAW,CAACtZ,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACzD,OAAO3D,QAAQ,CAACkd,IAAI,CAAC,CAAC;QAC1B;QAEA,OAAOld,QAAQ,CAAC/G,IAAI,CAAC,CAAC;MAC1B,CAAC,CAAC,SACI,CAAC,UAAAyC,KAAK,EAAI;QACZ;QACA,IAAIA,KAAK,CAACggB,IAAI,KAAK,YAAY,EAAE;UAC7B,MAAM,IAAI7Y,KAAK,CAAC,mBAAmB,CAAC;QACxC;;QAEA;QACA,IAAI1H,KAAI,CAACqf,OAAO,EAAE;UACdrf,KAAI,CAACqf,OAAO,CAAC9e,KAAK,CAAC;QACvB;QAEA,MAAMA,KAAK;MACf,CAAC,CAAC,WACM,CAAC,YAAM;QACX;QACAP,KAAI,CAACsf,eAAe,UAAO,CAACsB,SAAS,CAAC;;QAEtC;QACA,IAAI1iB,MAAM,IAAIojB,UAAU,EAAE;UACtB;UACA,IAAMW,uBAAuB,GAAG/b,KAAK,CAACC,IAAI,CAACnG,KAAI,CAACsf,eAAe,CAAC4C,MAAM,CAAC,CAAC,CAAC,CACpEC,IAAI,CAAC,UAAAC,GAAG;YAAA,OAAIA,GAAG,CAAClkB,MAAM,KAAKA,MAAM;UAAA,EAAC;UAEvC,IAAI,CAAC+jB,uBAAuB,EAAE;YAC1B/jB,MAAM,CAACoB,IAAI,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAA8jB,SAASA,CAAA,EAAG;MACR,IAAI,CAAC/C,eAAe,CAAC/d,OAAO,CAAC,UAAA+gB,OAAO,EAAI;QACpCA,OAAO,CAACxB,UAAU,CAACK,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAI,CAAC7B,eAAe,CAACiD,KAAK,CAAC,CAAC;IAChC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAjkB,GAAA;IAAAC,KAAA,EAKA,SAAAikB,WAAWA,CAACrD,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA7gB,GAAA;IAAAC,KAAA,EAMA,SAAOmL,MAAMA,CAAA,EAAe;MAAA,IAAdlM,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtB,OAAO,IAAImmB,WAAW,CAACpmB,OAAO,CAAC;IACnC;EAAC;AAAA,KAGL;AACA,IAAMymB,WAAW,GAAG,IAAIL,WAAW,CAAC,CAAC;AAErC,iEAAeK,WAAW,EAAC;;AAE3B;;;;;;;;;;;;;;;;;;;;;;;AC9MA;AACA;AACA;AACA;AACA;AACA;;AAEwC;AAAA,IAElCC,WAAW;EAAA,SAAAA,YAAA;IAAAtmB,eAAA,OAAAsmB,WAAA;EAAA;EAAA,OAAA7lB,YAAA,CAAA6lB,WAAA;IAAA5lB,GAAA;IAAAC,KAAA;IACb;AACJ;AACA;AACA;AACA;AACA;IACI,SAAA4lB,cAAcA,CAACC,MAAM,EAAE;MACnB,OAAOH,oDAAW,CAACzE,GAAG,WAAAtgB,MAAA,CAAWklB,MAAM,CAAE,CAAC;IAC9C;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA9lB,GAAA;IAAAC,KAAA,EAMA,SAAAgd,gBAAgBA,CAAC6I,MAAM,EAAE;MACrB,OAAOH,oDAAW,CAACzE,GAAG,WAAAtgB,MAAA,CAAWklB,MAAM,eAAY,CAAC;IACxD;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAA9lB,GAAA;IAAAC,KAAA,EAQA,SAAA4d,UAAUA,CAACiI,MAAM,EAAEnG,OAAO,EAAEoG,SAAS,EAAE;MACnC,OAAOJ,oDAAW,CAAChE,IAAI,WAAA/gB,MAAA,CAAWklB,MAAM,cAAW;QAC/CnG,OAAO,EAAEA,OAAO;QAChBD,UAAU,EAAEqG;MAChB,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA/lB,GAAA;IAAAC,KAAA,EAMA,SAAA+lB,iBAAiBA,CAAA,EAAe;MAAA,IAAdhB,OAAO,GAAA7lB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC1B,OAAOwmB,oDAAW,CAACzE,GAAG,CAAC,QAAQ,EAAE8D,OAAO,CAAC;IAC7C;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAhlB,GAAA;IAAAC,KAAA,EAOA,SAAAgmB,qBAAqBA,CAAA,EAAuB;MAAA,IAAtBf,KAAK,GAAA/lB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAAA,IAAE+mB,IAAI,GAAA/mB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MACtC,OAAOwmB,oDAAW,CAACzE,GAAG,CAAC,UAAU,EAAE;QAC/BgE,KAAK,EAAEA,KAAK;QACZgB,IAAI,EAAEA;MACV,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAlmB,GAAA;IAAAC,KAAA,EAMA,SAAAkmB,aAAaA,CAACC,QAAQ,EAAE;MACpB,OAAOT,oDAAW,CAACzE,GAAG,aAAAtgB,MAAA,CAAawlB,QAAQ,CAAE,CAAC;IAClD;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAApmB,GAAA;IAAAC,KAAA,EAOA,SAAAomB,gBAAgBA,CAACP,MAAM,EAAEQ,QAAQ,EAAE;MAC/B,OAAOX,oDAAW,CAAChE,IAAI,WAAA/gB,MAAA,CAAWklB,MAAM,gBAAa;QACjDQ,QAAQ,EAAEA,QAAQ;QAClBC,gBAAgB,EAAED,QAAQ,CAACE,eAAe,IAAI;MAClD,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAxmB,GAAA;IAAAC,KAAA,EAMA,SAAAwmB,gBAAgBA,CAACX,MAAM,EAAE;MACrB,OAAOH,oDAAW,CAACzE,GAAG,WAAAtgB,MAAA,CAAWklB,MAAM,cAAW,CAAC;IACvD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA9lB,GAAA;IAAAC,KAAA,EAMA,SAAAymB,iBAAiBA,CAACZ,MAAM,EAAE;MACtB,OAAOH,oDAAW,CAACzE,GAAG,WAAAtgB,MAAA,CAAWklB,MAAM,gBAAa,CAAC;IACzD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA9lB,GAAA;IAAAC,KAAA,EAMA,SAAA0mB,iBAAiBA,CAACC,SAAS,EAAE;MACzB;MACA,OAAOjlB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC9B;EAAC;AAAA,KAGL;AACA,IAAMsZ,WAAW,GAAG,IAAI0K,WAAW,CAAC,CAAC;AACrC,iEAAe1K,WAAW,EAAC;;AAE3B;;;;;;;UCjIA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAC2C;AACF;AACyB;AACA;AACZ;AACa;;AAEnE;AACAhb,QAAQ,CAACiD,gBAAgB,CAAC,kBAAkB,EAAE,YAAM;EAChD;EACAX,MAAM,CAACugB,WAAW,GAAG,IAAI9jB,0DAAM,CAAC;IAC5BS,UAAU,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA8C,MAAM,CAACqkB,SAAS,GAAG1C,6DAAW;EAC9B3hB,MAAM,CAACskB,SAAS,GAAG5L,6DAAW;;EAE9B;EACA,IAAM5M,SAAS,GAAGpO,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;EACvD,IAAImO,SAAS,EAAE;IACX9L,MAAM,CAACukB,kBAAkB,GAAG,IAAIjZ,4EAAqB,CAAC;MAACC,aAAa,EAAEO,SAAS,CAAC/I,OAAO,CAACwI;IAAa,CAAC,CAAC;EAC3G;AACJ,CAAC,CAAC;;AAEF", "sources": ["webpack://ToeicPractice/webpack/universalModuleDefinition", "webpack://ToeicPractice/./assets/js/frontend/src/components/Loader.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/Sidebar.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/MatchingRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/QuestionRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/QuestionRendererFactory.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/TextInputRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/index.js", "webpack://ToeicPractice/./assets/js/frontend/src/pages/pronunciation-page.js", "webpack://ToeicPractice/./assets/js/frontend/src/pages/test-detail-page.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/AjaxService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/PronunciationService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/RestService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/TestService.js", "webpack://ToeicPractice/webpack/bootstrap", "webpack://ToeicPractice/webpack/runtime/define property getters", "webpack://ToeicPractice/webpack/runtime/hasOwnProperty shorthand", "webpack://ToeicPractice/webpack/runtime/make namespace object", "webpack://ToeicPractice/./assets/js/frontend/src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ToeicPractice\"] = factory();\n\telse\n\t\troot[\"ToeicPractice\"] = factory();\n})(self, () => {\nreturn ", "/**\r\n * Loader Component\r\n * \r\n * A reusable loading indicator component that can be shown during\r\n * long-running processes like AJAX requests.\r\n */\r\nclass Loader {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.containerId - ID of the container element (default: 'toeic-loader-container')\r\n     * @param {string} options.text - Loading text to display (default: 'Loading...')\r\n     * @param {string} options.size - Size of the loader ('small', 'medium', 'large') (default: 'medium')\r\n     * @param {boolean} options.fullScreen - Whether to show loader as a fullscreen overlay (default: false)\r\n     */\r\n    constructor(options = {}) {\r\n        this.options = {\r\n            containerId: options.containerId || 'toeic-loader-container',\r\n            text: options.text || 'Loading...',\r\n            size: options.size || 'medium',\r\n            fullScreen: options.fullScreen || false\r\n        };\r\n        \r\n        this.container = null;\r\n        this.loader = null;\r\n        this.isVisible = false;\r\n        \r\n        this.init();\r\n    }\r\n    \r\n    /**\r\n     * Initialize the loader\r\n     */\r\n    init() {\r\n        // Check if container already exists\r\n        this.container = document.getElementById(this.options.containerId);\r\n        \r\n        // If container doesn't exist, create it\r\n        if (!this.container) {\r\n            this.container = document.createElement('div');\r\n            this.container.id = this.options.containerId;\r\n            this.container.className = 'toeic-loader-container';\r\n            \r\n            if (this.options.fullScreen) {\r\n                this.container.classList.add('fullscreen');\r\n            }\r\n            \r\n            document.body.appendChild(this.container);\r\n        }\r\n        \r\n        // Create loader element\r\n        this.createLoader();\r\n    }\r\n    \r\n    /**\r\n     * Create the loader element\r\n     */\r\n    createLoader() {\r\n        this.loader = document.createElement('div');\r\n        this.loader.className = `toeic-loader ${this.options.size}`;\r\n        \r\n        // Create spinner\r\n        const spinner = document.createElement('div');\r\n        spinner.className = 'toeic-spinner';\r\n        this.loader.appendChild(spinner);\r\n        \r\n        // Create text element if text is provided\r\n        if (this.options.text) {\r\n            const textElement = document.createElement('div');\r\n            textElement.className = 'toeic-loader-text';\r\n            textElement.textContent = this.options.text;\r\n            this.loader.appendChild(textElement);\r\n        }\r\n        \r\n        // Add to container but keep hidden initially\r\n        this.container.appendChild(this.loader);\r\n        this.hide();\r\n    }\r\n    \r\n    /**\r\n     * Show the loader\r\n     * \r\n     * @param {string} text - Optional text to update the loader with\r\n     */\r\n    show(text = null) {\r\n        if (text) {\r\n            this.updateText(text);\r\n        }\r\n        \r\n        this.container.classList.add('show');\r\n        this.isVisible = true;\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Hide the loader\r\n     */\r\n    hide() {\r\n        this.container.style.display = 'none';\r\n        this.isVisible = false;\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Update the loader text\r\n     * \r\n     * @param {string} text - New text to display\r\n     */\r\n    updateText(text) {\r\n        const textElement = this.loader.querySelector('.toeic-loader-text');\r\n        \r\n        if (textElement) {\r\n            textElement.textContent = text;\r\n        } else if (text) {\r\n            // Create text element if it doesn't exist\r\n            const newTextElement = document.createElement('div');\r\n            newTextElement.className = 'toeic-loader-text';\r\n            newTextElement.textContent = text;\r\n            this.loader.appendChild(newTextElement);\r\n        }\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Toggle the loader visibility\r\n     * \r\n     * @param {string} text - Optional text to update the loader with\r\n     */\r\n    toggle(text = null) {\r\n        if (this.isVisible) {\r\n            this.hide();\r\n        } else {\r\n            this.show(text);\r\n        }\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Show the loader for a specific duration\r\n     * \r\n     * @param {number} duration - Duration in milliseconds\r\n     * @param {string} text - Optional text to update the loader with\r\n     * @returns {Promise} - Promise that resolves when the loader is hidden\r\n     */\r\n    showFor(duration, text = null) {\r\n        return new Promise(resolve => {\r\n            this.show(text);\r\n            \r\n            setTimeout(() => {\r\n                this.hide();\r\n                resolve();\r\n            }, duration);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Create a loader instance for a specific element\r\n     * \r\n     * @param {HTMLElement|string} element - Element or element selector to show loader in\r\n     * @param {Object} options - Loader options\r\n     * @returns {Loader} - New Loader instance\r\n     */\r\n    static forElement(element, options = {}) {\r\n        // If element is a string, treat it as a selector\r\n        if (typeof element === 'string') {\r\n            element = document.querySelector(element);\r\n        }\r\n        \r\n        if (!element) {\r\n            console.error('Element not found for Loader.forElement');\r\n            return null;\r\n        }\r\n        \r\n        // Generate a unique ID for this loader\r\n        const uniqueId = 'toeic-loader-' + Math.random().toString(36).substr(2, 9);\r\n        \r\n        // Make sure the element has position relative for proper loader positioning\r\n        const computedStyle = window.getComputedStyle(element);\r\n        if (computedStyle.position === 'static') {\r\n            element.style.position = 'relative';\r\n        }\r\n        \r\n        // Create loader container inside the element\r\n        const container = document.createElement('div');\r\n        container.id = uniqueId;\r\n        container.className = 'toeic-loader-container';\r\n        element.appendChild(container);\r\n        \r\n        // Create and return loader instance\r\n        return new Loader({\r\n            ...options,\r\n            containerId: uniqueId,\r\n            fullScreen: false\r\n        });\r\n    }\r\n}\r\n\r\nexport default Loader;\r\n", "/**\r\n * Sidebar Component\r\n * \r\n * Handles the left sidebar navigation that's common across all pages\r\n * in the TOEIC Practice plugin frontend.\r\n */\r\nclass Sidebar {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {HTMLElement} element - The sidebar container element\r\n     */\r\n    constructor(element) {\r\n        this.element = element;\r\n        this.menuItems = this.element.querySelectorAll('.toeic-menu-item');\r\n    }\r\n\r\n    /**\r\n     * Initialize the sidebar\r\n     */\r\n    init() {\r\n        this.setupEventListeners();\r\n        this.highlightCurrentPage();\r\n    }\r\n\r\n    /**\r\n     * Set up event listeners for menu items\r\n     */\r\n    setupEventListeners() {\r\n        this.menuItems.forEach(item => {\r\n            item.addEventListener('click', this.handleMenuItemClick.bind(this));\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Handle menu item click\r\n     * \r\n     * @param {Event} event - Click event\r\n     */\r\n    handleMenuItemClick(event) {\r\n        // Remove active class from all menu items\r\n        this.menuItems.forEach(item => {\r\n            item.classList.remove('active');\r\n        });\r\n\r\n        // Add active class to clicked menu item\r\n        event.currentTarget.classList.add('active');\r\n    }\r\n\r\n    /**\r\n     * Highlight the current page in the menu\r\n     */\r\n    highlightCurrentPage() {\r\n        // Get current page path\r\n        const currentPath = window.location.pathname;\r\n        \r\n        // Find and highlight the corresponding menu item\r\n        this.menuItems.forEach(item => {\r\n            const link = item.querySelector('a');\r\n            if (link && link.getAttribute('href') === currentPath) {\r\n                item.classList.add('active');\r\n            }\r\n        });\r\n    }\r\n}\r\n\r\nexport default Sidebar;\r\n", "/**\r\n * Matching Question Renderer\r\n * \r\n * Handles rendering of matching questions where users match items from two columns.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass MatchingRenderer extends QuestionRenderer {\r\n    /**\r\n     * Render the matching question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n\r\n        // Parse items if needed\r\n        let items = [];\r\n        try {\r\n            items = typeof this.question.items === 'string' ? \r\n                JSON.parse(this.question.items) : this.question.items;\r\n        } catch (e) {\r\n            console.error('Error parsing matching items:', e);\r\n            return;\r\n        }\r\n        \r\n        // If no items found, return\r\n        if (!items || !items.length) {\r\n            console.error('No items found for matching question:', this.question.id);\r\n            return;\r\n        }\r\n        \r\n        // Create matching container\r\n        const matchingContainer = document.createElement('div');\r\n        matchingContainer.className = 'toeic-matching-container';\r\n        \r\n        // Get user answers\r\n        const userAnswers = this.getUserAnswer() || {};\r\n        \r\n        // Create left column (English words)\r\n        const leftColumn = document.createElement('div');\r\n        leftColumn.className = 'toeic-matching-column toeic-matching-left';\r\n        \r\n        // Create right column (Vietnamese words)\r\n        const rightColumn = document.createElement('div');\r\n        rightColumn.className = 'toeic-matching-column toeic-matching-right';\r\n\r\n        // Add column headers\r\n        const leftHeader = document.createElement('div');\r\n        leftHeader.className = 'toeic-matching-header';\r\n        leftHeader.textContent = 'English';\r\n        leftColumn.appendChild(leftHeader);\r\n\r\n        const rightHeader = document.createElement('div');\r\n        rightHeader.className = 'toeic-matching-header';\r\n        rightHeader.textContent = 'Vietnamese';\r\n        rightColumn.appendChild(rightHeader);\r\n\r\n        // Track selected items for matching\r\n        this.selectedLeft = null;\r\n        this.selectedRight = null;\r\n        \r\n        // Track total number of pairs and paired items\r\n        this.totalPairs = items.length;\r\n        this.pairedCount = 0;\r\n\r\n        // Add items to columns\r\n        items.forEach((item, index) => {\r\n            // Left column item (English word)\r\n            const leftItem = document.createElement('div');\r\n            leftItem.className = 'toeic-matching-card';\r\n            leftItem.dataset.itemId = item.id;\r\n            \r\n            const wordContainer = document.createElement('div');\r\n            wordContainer.className = 'toeic-matching-word-container';\r\n            \r\n            // Add the word text\r\n            const wordText = document.createElement('span');\r\n            wordText.className = 'toeic-matching-word';\r\n            wordText.textContent = item.prompt;\r\n            wordContainer.appendChild(wordText);\r\n            \r\n            // Add audio button if available\r\n            if (item.audio_link) {\r\n                const audioButton = document.createElement('button');\r\n                audioButton.className = 'toeic-matching-audio-btn';\r\n                audioButton.innerHTML = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M11.536 14.01A8.473 8.473 0 0 0 14.026 8a8.473 8.473 0 0 0-2.49-6.01l-.708.707A7.476 7.476 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303l.708.707z\"/><path d=\"M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.483 5.483 0 0 1 11.025 8a5.483 5.483 0 0 1-1.61 3.89l.706.706z\"/><path d=\"M8.707 11.182A4.486 4.486 0 0 0 10.025 8a4.486 4.486 0 0 0-1.318-3.182L8 5.525A3.489 3.489 0 0 1 9.025 8 3.49 3.49 0 0 1 8 10.475l.707.707zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z\"/></svg>';\r\n                \r\n                audioButton.addEventListener('click', (e) => {\r\n                    e.stopPropagation(); // Prevent card selection when clicking audio button\r\n                    const audio = new Audio(item.audio_link);\r\n                    audio.play().catch(e => {\r\n                        console.error('Error playing audio:', e);\r\n                    });\r\n                });\r\n                \r\n                wordContainer.appendChild(audioButton);\r\n            }\r\n            \r\n            leftItem.appendChild(wordContainer);\r\n            \r\n            // Add click event for selection\r\n            leftItem.addEventListener('click', () => {\r\n                if (this.isReviewMode) return;\r\n                \r\n                // Toggle selection\r\n                this.toggleLeftSelection(leftItem);\r\n                \r\n                // Check if we have a match\r\n                this.checkForMatch();\r\n            });\r\n            \r\n            leftColumn.appendChild(leftItem);\r\n            \r\n            // Right column item (Vietnamese translation)\r\n            const rightItem = document.createElement('div');\r\n            rightItem.className = 'toeic-matching-card';\r\n            rightItem.dataset.itemId = item.id;\r\n            rightItem.textContent = item.response;\r\n            \r\n            // Add click event for selection\r\n            rightItem.addEventListener('click', () => {\r\n                if (this.isReviewMode) return;\r\n\r\n                console.log('Right item clicked');\r\n                \r\n                // Toggle selection\r\n                this.toggleRightSelection(rightItem);\r\n                \r\n                // Check if we have a match\r\n                this.checkForMatch();\r\n            });\r\n            \r\n            rightColumn.appendChild(rightItem);\r\n        });\r\n        \r\n        // Add columns to container\r\n        matchingContainer.appendChild(leftColumn);\r\n        matchingContainer.appendChild(rightColumn);\r\n        \r\n        // Add to main container\r\n        this.container.appendChild(matchingContainer);\r\n        \r\n        // Create submit button (initially hidden)\r\n        const submitButton = document.createElement('button');\r\n        submitButton.className = 'toeic-submit-btn';\r\n        submitButton.textContent = 'Submit Answers';\r\n        submitButton.disabled = true;\r\n        submitButton.addEventListener('click', () => {\r\n            // Trigger submission of the question\r\n            if (typeof this.options.onSubmit === 'function') {\r\n                this.options.onSubmit(this.question.id);\r\n            }\r\n        });\r\n        this.container.appendChild(submitButton);\r\n        this.submitButton = submitButton;\r\n        \r\n        // Add CSS for the matching cards\r\n        this.addStyles();\r\n        \r\n        // In review mode, show the correct matches\r\n        if (this.isReviewMode) {\r\n            this.showCorrectMatches(items, userAnswers);\r\n        } else {\r\n            // If we have existing answers, show them\r\n            this.showExistingAnswers(items, userAnswers);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Add custom styles for matching cards\r\n     */\r\n    addStyles() {\r\n        // Check if styles are already added\r\n        if (document.getElementById('toeic-matching-styles')) return;\r\n        \r\n        const styleEl = document.createElement('style');\r\n        styleEl.id = 'toeic-matching-styles';\r\n        styleEl.textContent = `\r\n            .toeic-matching-container {\r\n                display: flex;\r\n                gap: 20px;\r\n                margin-bottom: 20px;\r\n            }\r\n            \r\n            .toeic-matching-column {\r\n                flex: 1;\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 10px;\r\n            }\r\n            \r\n            .toeic-matching-header {\r\n                font-weight: bold;\r\n                font-size: 16px;\r\n                padding: 10px;\r\n                background-color: #f5f5f5;\r\n                border-radius: 5px;\r\n                text-align: center;\r\n            }\r\n            \r\n            .toeic-matching-card {\r\n                padding: 15px;\r\n                border-radius: 5px;\r\n                background-color: #fff;\r\n                box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                cursor: pointer;\r\n                transition: all 0.3s ease;\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                height: 60px;\r\n                font-size: 24px;\r\n            }\r\n            \r\n            .toeic-matching-word-container {\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                width: 100%;\r\n            }\r\n            \r\n            .toeic-matching-card:hover:not(.paired):not(.disabled) {\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                transform: translateY(-2px);\r\n            }\r\n            \r\n            .toeic-matching-card.selected {\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                transform: translateY(-2px);\r\n                box-shadow: 0 4px 8px rgba(0,0,0,0.2);\r\n            }\r\n            \r\n            .toeic-matching-card.fade-out {\r\n                animation: fadeOut 0.8s forwards;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            @keyframes fadeOut {\r\n                0% { opacity: 1; transform: scale(1); }\r\n                50% { opacity: 0.8; transform: scale(1.05); }\r\n                100% { opacity: 0.4; transform: scale(0.95); }\r\n            }\r\n            \r\n            .toeic-matching-card.paired {\r\n                opacity: 0.4;\r\n                transform: scale(0.95);\r\n                background-color: #f0f0f0;\r\n                color: #666;\r\n                border: 1px dashed #ccc;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            .toeic-matching-card.disabled {\r\n                cursor: default;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            .toeic-matching-audio-btn {\r\n                background: transparent;\r\n                border: none;\r\n                color: inherit;\r\n                cursor: pointer;\r\n                padding: 5px;\r\n                margin-left: 10px;\r\n                width: 30px;\r\n                height: 30px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border-radius: 50%;\r\n                transition: all 0.2s ease;\r\n            }\r\n            \r\n            .toeic-matching-audio-btn:hover {\r\n                background-color: rgba(255, 255, 255, 0.3);\r\n                transform: scale(1.1);\r\n            }\r\n            \r\n            .toeic-matching-word {\r\n                flex: 1;\r\n            }\r\n            \r\n            .toeic-submit-btn {\r\n                margin-top: 20px;\r\n                padding: 10px 20px;\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                border: none;\r\n                border-radius: 5px;\r\n                cursor: pointer;\r\n                font-weight: bold;\r\n                display: none;\r\n            }\r\n            \r\n            .toeic-submit-btn.active {\r\n                display: block;\r\n            }\r\n            \r\n            .toeic-submit-btn:disabled {\r\n                background: #cccccc;\r\n                cursor: not-allowed;\r\n            }\r\n        `;\r\n        \r\n        document.head.appendChild(styleEl);\r\n    }\r\n    \r\n    /**\r\n     * Toggle selection of a left column item\r\n     * \r\n     * @param {HTMLElement} item - The item to toggle\r\n     */\r\n    toggleLeftSelection(item) {\r\n        // If item is already paired or disabled, do nothing\r\n        if (item.classList.contains('paired') || item.classList.contains('disabled')) {\r\n            return;\r\n        }\r\n        \r\n        // If this item is already selected, deselect it\r\n        if (this.selectedLeft === item) {\r\n            item.classList.remove('selected');\r\n            this.selectedLeft = null;\r\n            return;\r\n        }\r\n        \r\n        // Deselect any previously selected item\r\n        if (this.selectedLeft) {\r\n            this.selectedLeft.classList.remove('selected');\r\n        }\r\n        \r\n        // Select this item\r\n        item.classList.add('selected');\r\n        this.selectedLeft = item;\r\n    }\r\n    \r\n    /**\r\n     * Toggle selection of a right column item\r\n     * \r\n     * @param {HTMLElement} item - The item to toggle\r\n     */\r\n    toggleRightSelection(item) {\r\n        // If item is already paired or disabled, do nothing\r\n        if (item.classList.contains('paired') || item.classList.contains('disabled')) {\r\n            return;\r\n        }\r\n        \r\n        // If this item is already selected, deselect it\r\n        if (this.selectedRight === item) {\r\n            item.classList.remove('selected');\r\n            this.selectedRight = null;\r\n            return;\r\n        }\r\n        \r\n        // Deselect any previously selected item\r\n        if (this.selectedRight) {\r\n            this.selectedRight.classList.remove('selected');\r\n        }\r\n        \r\n        // Select this item\r\n        item.classList.add('selected');\r\n        this.selectedRight = item;\r\n    }\r\n    \r\n    /**\r\n     * Check if we have a match between selected items\r\n     */\r\n    checkForMatch() {\r\n        console.log('Checking for match...');\r\n        if (!this.selectedLeft || !this.selectedRight) return;\r\n        \r\n        const leftId = this.selectedLeft.dataset.itemId;\r\n        const rightId = this.selectedRight.dataset.itemId;\r\n        \r\n        // Update user answer\r\n        const answer = this.getUserAnswer() || {};\r\n        answer[leftId] = rightId;\r\n        this.setUserAnswer(answer);\r\n        \r\n        // Apply fade out animation to both cards\r\n        this.selectedLeft.classList.add('fade-out');\r\n        this.selectedRight.classList.add('fade-out');\r\n        \r\n        // Mark cards as paired (regardless of correct or not)\r\n        this.selectedLeft.classList.add('paired');\r\n        this.selectedRight.classList.add('paired');\r\n        this.selectedLeft.classList.remove('selected');\r\n        this.selectedRight.classList.remove('selected');\r\n        \r\n        // Disable paired cards\r\n        this.selectedLeft.classList.add('disabled');\r\n        this.selectedRight.classList.add('disabled');\r\n        \r\n        // Reset selection\r\n        this.selectedLeft = null;\r\n        this.selectedRight = null;\r\n        \r\n        // Check if all pairs have been matched\r\n        this.checkAllPaired();\r\n    }\r\n\r\n    /**\r\n     * Set user answer\r\n     * \r\n     * @param {Object} answer - The user's answer\r\n     */\r\n    setUserAnswer(answer) {\r\n        this.userAnswer = answer;\r\n    }\r\n    \r\n    /**\r\n     * Show correct matches in review mode\r\n     * \r\n     * @param {Array} items - The items array\r\n     * @param {Object} userAnswers - User's answers\r\n     */\r\n    showCorrectMatches(items, userAnswers) {\r\n        const leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');\r\n        const rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');\r\n        \r\n        items.forEach(item => {\r\n            const leftCard = Array.from(leftCards).find(card => card.dataset.itemId === item.id);\r\n            const rightCard = Array.from(rightCards).find(card => card.dataset.itemId === item.id);\r\n            \r\n            if (leftCard && rightCard) {\r\n                const userAnswer = userAnswers[item.id];\r\n                \r\n                if (userAnswer === item.id) {\r\n                    // Correct match\r\n                    leftCard.classList.add('matched');\r\n                    rightCard.classList.add('matched');\r\n                } else if (userAnswer) {\r\n                    // Incorrect match\r\n                    leftCard.classList.add('incorrect');\r\n                    \r\n                    // Find the incorrectly matched right card\r\n                    const wrongRightCard = Array.from(rightCards).find(card => card.dataset.itemId === userAnswer);\r\n                    if (wrongRightCard) {\r\n                        wrongRightCard.classList.add('incorrect');\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Show existing answers\r\n     * \r\n     * @param {Array} items - The items array\r\n     * @param {Object} userAnswers - User's answers\r\n     */\r\n    showExistingAnswers(items, userAnswers) {\r\n        const leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');\r\n        const rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');\r\n        \r\n        let pairedCount = 0;\r\n        \r\n        items.forEach(item => {\r\n            const leftCard = Array.from(leftCards).find(card => card.dataset.itemId === item.id);\r\n            const userAnswer = userAnswers[item.id];\r\n            \r\n            if (leftCard && userAnswer) {\r\n                const rightCard = Array.from(rightCards).find(card => card.dataset.itemId === userAnswer);\r\n                \r\n                if (rightCard) {\r\n                    // Mark as paired\r\n                    leftCard.classList.add('paired');\r\n                    rightCard.classList.add('paired');\r\n                    leftCard.classList.add('disabled');\r\n                    rightCard.classList.add('disabled');\r\n                    pairedCount++;\r\n                }\r\n            }\r\n        });\r\n        \r\n        // Update paired count\r\n        this.pairedCount = pairedCount;\r\n        \r\n        // Check if all pairs are matched\r\n        this.checkAllPaired();\r\n    }\r\n    \r\n    /**\r\n     * Check if all pairs have been matched\r\n     */\r\n    checkAllPaired() {\r\n        if (this.pairedCount >= this.totalPairs) {\r\n            // All pairs have been matched, show submit button\r\n            if (this.submitButton) {\r\n                this.submitButton.classList.add('active');\r\n                this.submitButton.disabled = false;\r\n            }\r\n        } else {\r\n            // Not all pairs matched yet\r\n            if (this.submitButton) {\r\n                this.submitButton.disabled = true;\r\n            }\r\n        }\r\n        \r\n        // Update paired count\r\n        this.pairedCount = this.container.querySelectorAll('.toeic-matching-card.paired').length / 2;\r\n    }\r\n    \r\n    /**\r\n     * Shuffle the response items to prevent ordering hints\r\n     * \r\n     * @param {Array} items - The original items array\r\n     * @returns {Array} Shuffled array of response items\r\n     */\r\n    shuffleResponses(items) {\r\n        // Extract just the response data needed\r\n        const responses = items.map(item => ({\r\n            id: item.id,\r\n            response: item.response\r\n        }));\r\n        \r\n        // Shuffle using Fisher-Yates algorithm\r\n        // Only shuffle in non-review mode to keep consistent ordering in review\r\n        if (!this.isReviewMode) {\r\n            for (let i = responses.length - 1; i > 0; i--) {\r\n                const j = Math.floor(Math.random() * (i + 1));\r\n                [responses[i], responses[j]] = [responses[j], responses[i]];\r\n            }\r\n        }\r\n        \r\n        return responses;\r\n    }\r\n}\r\n\r\nexport default MatchingRenderer;\r\n", "/**\r\n * Multiple Choice Question Renderer\r\n * \r\n * Handles rendering of multiple choice questions with radio button options.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass MultipleC<PERSON>ice<PERSON><PERSON><PERSON> extends Question<PERSON><PERSON>er {\r\n    /**\r\n     * Render the multiple choice question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n        \r\n        // Parse options if needed\r\n        let options = [];\r\n        try {\r\n            options = typeof this.question.options === 'string' ? \r\n                JSON.parse(this.question.options) : this.question.options;\r\n        } catch (e) {\r\n            console.error('Error parsing question options:', e);\r\n            return;\r\n        }\r\n        \r\n        // If no options found, return\r\n        if (!options || !options.length) {\r\n            console.error('No options found for multiple choice question:', this.question.id);\r\n            return;\r\n        }\r\n        \r\n        // Create option elements\r\n        options.forEach((option, index) => {\r\n            const optionElement = this.createOptionElement(option, index);\r\n            this.container.appendChild(optionElement);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Create an option element for a multiple choice question\r\n     * \r\n     * @param {Object} option - The option object with value and text\r\n     * @param {number} index - The index of the option\r\n     * @returns {HTMLElement} The created option element\r\n     */\r\n    createOptionElement(option, index) {\r\n        const optionElement = document.createElement('div');\r\n        optionElement.className = 'toeic-question-option';\r\n        \r\n        const inputId = `question-${this.question.id}-option-${index}`;\r\n        const isChecked = this.getUserAnswer() === option.value;\r\n        \r\n        optionElement.innerHTML = `\r\n            <input type=\"radio\" \r\n                   id=\"${inputId}\" \r\n                   name=\"question-${this.question.id}\" \r\n                   value=\"${option.value}\" \r\n                   ${isChecked ? 'checked' : ''}\r\n                   ${this.isReviewMode ? 'disabled' : ''}\r\n            >\r\n            <label for=\"${inputId}\">${option.text}</label>\r\n        `;\r\n        \r\n        // In review mode, highlight correct and incorrect answers\r\n        if (this.isReviewMode && this.question.correct_answer) {\r\n            if (option.value === this.question.correct_answer) {\r\n                optionElement.classList.add('correct');\r\n            } else if (isChecked) {\r\n                optionElement.classList.add('incorrect');\r\n            }\r\n        }\r\n        \r\n        // Add event listener for option selection\r\n        if (!this.isReviewMode) {\r\n            const input = optionElement.querySelector('input');\r\n            if (input && typeof this.options.onAnswerSelected === 'function') {\r\n                input.addEventListener('change', () => {\r\n                    this.options.onAnswerSelected(this.question.id, option.value);\r\n                });\r\n            }\r\n        }\r\n        \r\n        return optionElement;\r\n    }\r\n}\r\n\r\nexport default MultipleChoiceRenderer;\r\n", "/**\r\n * Base Question Renderer\r\n * \r\n * Abstract base class for all question type renderers.\r\n * Each specific question type should extend this class.\r\n */\r\n\r\nclass Question<PERSON>enderer {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} question - The question object to render\r\n     * @param {HTMLElement} container - The container element to render into\r\n     * @param {Object} options - Additional options for rendering\r\n     */\r\n    constructor(question, container, options = {}) {\r\n        if (new.target === QuestionRenderer) {\r\n            throw new Error('QuestionRenderer is an abstract class and cannot be instantiated directly');\r\n        }\r\n        \r\n        this.question = question;\r\n        this.container = container;\r\n        this.options = options;\r\n        this.isReviewMode = options.isReviewMode || false;\r\n        this.userAnswers = options.userAnswers || {};\r\n    }\r\n    \r\n    /**\r\n     * Render the question\r\n     * This method must be implemented by subclasses\r\n     */\r\n    render() {\r\n        throw new Error('render() method must be implemented by subclass');\r\n    }\r\n    \r\n    /**\r\n     * Get the user's answer for this question\r\n     * \r\n     * @returns {*} The user's answer or null if not answered\r\n     */\r\n    getUserAnswer() {\r\n        return this.userAnswers[this.question.id] || null;\r\n    }\r\n    \r\n    /**\r\n     * Check if the question has been answered\r\n     * \r\n     * @returns {boolean} True if the question has been answered\r\n     */\r\n    isAnswered() {\r\n        return this.question.id in this.userAnswers;\r\n    }\r\n    \r\n    /**\r\n     * Clear the container before rendering\r\n     */\r\n    clearContainer() {\r\n        if (this.container) {\r\n            this.container.innerHTML = '';\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Create a basic wrapper element for the question\r\n     * \r\n     * @returns {HTMLElement} The wrapper element\r\n     */\r\n    createWrapper() {\r\n        const wrapper = document.createElement('div');\r\n        wrapper.className = 'toeic-question-wrapper';\r\n        wrapper.dataset.questionId = this.question.id;\r\n        wrapper.dataset.questionType = this.question.type;\r\n        return wrapper;\r\n    }\r\n}\r\n\r\nexport default QuestionRenderer;\r\n", "/**\r\n * Question Renderer Factory\r\n * \r\n * Factory class that creates the appropriate renderer for a given question type.\r\n */\r\n\r\nimport MultipleC<PERSON>iceRenderer from './MultipleChoiceRenderer';\r\nimport TextInputRenderer from './TextInputRenderer';\r\nimport MatchingRenderer from './MatchingRenderer';\r\n\r\nclass QuestionRendererFactory {\r\n    /**\r\n     * Create a renderer instance for the given question\r\n     * \r\n     * @param {Object} question - The question object\r\n     * @param {HTMLElement} container - The container to render into\r\n     * @param {Object} options - Additional options for rendering\r\n     * @returns {QuestionRenderer} An instance of the appropriate renderer\r\n     * @throws {Error} If no renderer is available for the question type\r\n     */\r\n    static createRenderer(question, container, options = {}) {\r\n        if (!question || !question.question_type) {\r\n            throw new Error('Question object is invalid or missing type');\r\n        }\r\n        \r\n        // Select the appropriate renderer based on question type\r\n        switch (question.question_type.toLowerCase()) {\r\n            case 'multiple_choice':\r\n                return new MultipleChoiceRenderer(question, container, options);\r\n                \r\n            case 'text_input':\r\n            case 'short_answer':\r\n            case 'fill_in_blank':\r\n                return new TextInputRenderer(question, container, options);\r\n                \r\n            case 'matching':\r\n                return new MatchingRenderer(question, container, options);\r\n                \r\n            default:\r\n                throw new Error(`No renderer available for question type: ${question.question_type}`);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Check if a renderer is available for the given question type\r\n     * \r\n     * @param {string} questionType - The question type to check\r\n     * @returns {boolean} True if a renderer is available\r\n     */\r\n    static hasRendererForType(questionType) {\r\n        if (!questionType) return false;\r\n        \r\n        const supportedTypes = [\r\n            'multiple_choice',\r\n            'text_input',\r\n            'short_answer',\r\n            'fill_in_blank',\r\n            'matching'\r\n        ];\r\n        \r\n        return supportedTypes.includes(questionType.toLowerCase());\r\n    }\r\n}\r\n\r\nexport default QuestionRendererFactory;\r\n", "/**\r\n * Text Input Question Renderer\r\n * \r\n * Handles rendering of text input questions where users type their answers.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass TextInputRenderer extends Question<PERSON>enderer {\r\n    /**\r\n     * Render the text input question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n        \r\n        // Create wrapper\r\n        const wrapper = this.createWrapper();\r\n        \r\n        // Create input element\r\n        const inputContainer = document.createElement('div');\r\n        inputContainer.className = 'toeic-question-input-container';\r\n        \r\n        const inputId = `question-${this.question.id}-input`;\r\n        const userAnswer = this.getUserAnswer() || '';\r\n        \r\n        inputContainer.innerHTML = `\r\n            <input type=\"text\" \r\n                   id=\"${inputId}\" \r\n                   class=\"toeic-question-text-input\"\r\n                   value=\"${userAnswer}\"\r\n                   placeholder=\"${this.question.placeholder || 'Type your answer here...'}\"\r\n                   ${this.isReviewMode ? 'disabled' : ''}\r\n            >\r\n        `;\r\n        \r\n        // In review mode, show correct answer and highlight\r\n        if (this.isReviewMode && this.question.correct_answer) {\r\n            const reviewInfo = document.createElement('div');\r\n            reviewInfo.className = 'toeic-question-review-info';\r\n            \r\n            const isCorrect = userAnswer.toLowerCase() === this.question.correct_answer.toLowerCase();\r\n            reviewInfo.classList.add(isCorrect ? 'correct' : 'incorrect');\r\n            \r\n            reviewInfo.innerHTML = `\r\n                <div class=\"toeic-question-correct-answer\">\r\n                    <strong>Correct answer:</strong> ${this.question.correct_answer}\r\n                </div>\r\n            `;\r\n            \r\n            inputContainer.appendChild(reviewInfo);\r\n        }\r\n        \r\n        wrapper.appendChild(inputContainer);\r\n        this.container.appendChild(wrapper);\r\n        \r\n        // Add event listener for input changes\r\n        if (!this.isReviewMode) {\r\n            const input = wrapper.querySelector('input');\r\n            if (input && typeof this.options.onAnswerSelected === 'function') {\r\n                input.addEventListener('change', (e) => {\r\n                    this.options.onAnswerSelected(this.question.id, e.target.value);\r\n                });\r\n                \r\n                // Also listen for input event to capture typing in real-time\r\n                input.addEventListener('input', (e) => {\r\n                    this.options.onAnswerSelected(this.question.id, e.target.value);\r\n                });\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nexport default TextInputRenderer;\r\n", "/**\r\n * Question Renderers Index\r\n * \r\n * Exports all question renderers and the factory.\r\n */\r\n\r\nimport QuestionRenderer from './QuestionRenderer';\r\nimport MultipleC<PERSON>iceRenderer from './MultipleChoiceRenderer';\r\nimport TextInputRenderer from './TextInputRenderer';\r\nimport MatchingRenderer from './MatchingRenderer';\r\nimport QuestionRendererFactory from './QuestionRendererFactory';\r\n\r\nexport {\r\n    QuestionRenderer,\r\n    MultipleChoiceRenderer,\r\n    TextInputRenderer,\r\n    MatchingRenderer,\r\n    QuestionRendererFactory\r\n};\r\n", "// pronunciation-page.js\n\nimport PronunciationService from '../services/PronunciationService';\n\nclass PronunciationRecorder {\n    constructor({paragraphText}) {\n        this.isRecording = false;\n        this.mediaRecorder = null;\n        this.audioChunks = [];\n        this.startTime = null;\n        this.timerInterval = null;\n        this.paragraphText = paragraphText;\n        \n        // Initialize pronunciation service\n        this.pronunciationService = new PronunciationService();\n        \n        // DOM elements\n        this.recordBtn = document.getElementById('record-btn');\n        this.recordingStatus = document.querySelector('.recording-status');\n        this.recordingDuration = document.querySelector('.recording-duration');\n        this.durationText = document.querySelector('.recording-duration-text');\n        this.suggestionBtns = document.querySelectorAll('.suggestion-btn');\n        \n        this.init();\n    }\n    \n    init() {\n        // Check if required elements exist\n        if (!this.recordBtn) {\n            console.warn('Record button not found');\n            return;\n        }\n\n        console.log('Record button found');\n        \n        // Initially hide recording status elements\n        this.hideRecordingElements();\n        \n        // Bind event listeners\n        this.recordBtn.addEventListener('click', () => this.toggleRecording());\n        this.suggestionBtns.forEach(button => button.addEventListener('click', (e) => this.handleSuggestion(e)));\n        \n        // Check for microphone permissions\n        this.checkMicrophonePermissions();\n    }\n    \n    async checkMicrophonePermissions() {\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n            stream.getTracks().forEach(track => track.stop());\n            console.log('Microphone access granted');\n        } catch (error) {\n            console.error('Microphone access denied:', error);\n            this.showError('Microphone access is required for recording. Please allow microphone permissions.');\n        }\n    }\n    \n    async toggleRecording() {\n        if (this.isRecording) {\n            this.stopRecording();\n        } else {\n            await this.startRecording();\n        }\n    }\n    \n    async startRecording() {\n        try {\n            // Get user media with optimized settings for speech (16kHz, mono)\n            const stream = await navigator.mediaDevices.getUserMedia({ \n                audio: {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    sampleRate: 16000, // Reduced from 44100 to 16000 for speech\n                    channelCount: 1     // Mono channel for speech\n                }\n            });\n            \n            // Create MediaRecorder with fallback MIME types\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Let browser choose\n                    }\n                }\n            }\n            \n            this.mediaRecorder = new MediaRecorder(stream, {\n                mimeType: mimeType\n            });\n            \n            // Store the actual MIME type used\n            this.actualMimeType = mimeType;\n            \n            // Reset audio chunks\n            this.audioChunks = [];\n            \n            // Set up event handlers\n            this.mediaRecorder.ondataavailable = (event) => {\n                if (event.data.size > 0) {\n                    this.audioChunks.push(event.data);\n                }\n            };\n            \n            this.mediaRecorder.onstop = () => {\n                this.processRecording();\n            };\n            \n            // Start recording\n            this.mediaRecorder.start();\n            this.isRecording = true;\n            this.startTime = Date.now();\n            \n            // Update UI\n            this.showRecordingElements();\n            this.updateButtonState();\n            this.startTimer();\n        } catch (error) {\n            console.error('Error starting recording:', error);\n            this.showError('Failed to start recording. Please check your microphone permissions.');\n        }\n    }\n    \n    stopRecording() {\n        if (this.mediaRecorder && this.isRecording) {\n            this.mediaRecorder.stop();\n            this.isRecording = false;\n            \n            // Stop all tracks\n            if (this.mediaRecorder.stream) {\n                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n            }\n            \n            // Update UI\n            this.hideRecordingElements();\n            this.updateButtonState();\n            this.stopTimer();\n            \n            console.log('Recording stopped');\n        }\n    }\n    \n    processRecording() {\n        if (this.audioChunks.length === 0) {\n            this.showError('No audio data recorded');\n            return;\n        }\n        \n        // Create blob with the correct MIME type that was actually recorded\n        const audioBlob = new Blob(this.audioChunks, { \n            type: this.actualMimeType || 'audio/webm' \n        });\n\n        // Convert to WAV if needed for better compatibility\n        this.convertToWAV(audioBlob).then(wavBlob => {\n            this.submitForGrading(wavBlob);\n        }).catch(error => {\n            console.warn('WAV conversion failed, using original format:', error);\n            // Fallback to original blob if conversion fails\n            this.submitForGrading(audioBlob);\n        });\n    }\n    \n    async convertToWAV(audioBlob) {\n        return new Promise((resolve, reject) => {\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const fileReader = new FileReader();\n            \n            fileReader.onload = async (e) => {\n                try {\n                    // Decode the audio data\n                    const arrayBuffer = e.target.result;\n                    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n                    \n                    // Trim silence from the audio buffer\n                    const trimmedBuffer = this.trimSilence(audioBuffer, audioContext);\n                    \n                    // Convert to WAV\n                    const wavBlob = this.audioBufferToWav(trimmedBuffer);\n                    resolve(wavBlob);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            \n            fileReader.onerror = () => reject(new Error('Failed to read audio file'));\n            fileReader.readAsArrayBuffer(audioBlob);\n        });\n    }\n    \n    /**\n     * Trim silence from the beginning and end of an audio buffer\n     * @param {AudioBuffer} audioBuffer - The original audio buffer\n     * @param {AudioContext} audioContext - The audio context to use for creating new buffers\n     * @param {number} silenceThreshold - Threshold for silence detection (0-1, default: 0.01)\n     * @param {number} marginMs - Margin to keep before/after speech in milliseconds (default: 100ms)\n     * @returns {AudioBuffer} - Trimmed audio buffer\n     */\n    trimSilence(audioBuffer, audioContext, silenceThreshold = 0.01, marginMs = 100) {\n        const sampleRate = audioBuffer.sampleRate;\n        const numberOfChannels = audioBuffer.numberOfChannels;\n        const length = audioBuffer.length;\n        \n        // Convert margin from milliseconds to samples\n        const marginSamples = Math.floor((marginMs / 1000) * sampleRate);\n        \n        // Get audio data for analysis (use first channel if stereo)\n        const audioData = audioBuffer.getChannelData(0);\n        \n        // Find start of speech (first non-silent sample)\n        let startIndex = 0;\n        for (let i = 0; i < length; i++) {\n            if (Math.abs(audioData[i]) > silenceThreshold) {\n                startIndex = Math.max(0, i - marginSamples);\n                break;\n            }\n        }\n        \n        // Find end of speech (last non-silent sample)\n        let endIndex = length - 1;\n        for (let i = length - 1; i >= 0; i--) {\n            if (Math.abs(audioData[i]) > silenceThreshold) {\n                endIndex = Math.min(length - 1, i + marginSamples);\n                break;\n            }\n        }\n        \n        // If no speech detected, return a minimal buffer\n        if (startIndex >= endIndex) {\n            console.warn('No speech detected in audio, returning minimal buffer');\n            const minimalLength = Math.floor(sampleRate * 0.1); // 100ms minimal buffer\n            const minimalBuffer = audioContext.createBuffer(\n                numberOfChannels, \n                minimalLength, \n                sampleRate\n            );\n            return minimalBuffer;\n        }\n        \n        // Calculate new buffer length\n        const newLength = endIndex - startIndex + 1;\n        \n        // Create new trimmed buffer\n        const trimmedBuffer = audioContext.createBuffer(\n            numberOfChannels,\n            newLength,\n            sampleRate\n        );\n        \n        // Copy trimmed audio data for all channels\n        for (let channel = 0; channel < numberOfChannels; channel++) {\n            const originalData = audioBuffer.getChannelData(channel);\n            const trimmedData = trimmedBuffer.getChannelData(channel);\n            \n            for (let i = 0; i < newLength; i++) {\n                trimmedData[i] = originalData[startIndex + i];\n            }\n        }\n        \n        // Log trimming results\n        const originalDuration = (length / sampleRate * 1000).toFixed(0);\n        const trimmedDuration = (newLength / sampleRate * 1000).toFixed(0);\n        const trimmedStart = (startIndex / sampleRate * 1000).toFixed(0);\n        const trimmedEnd = ((length - endIndex - 1) / sampleRate * 1000).toFixed(0);\n        \n        console.log(`Audio trimmed: ${originalDuration}ms → ${trimmedDuration}ms (removed ${trimmedStart}ms from start, ${trimmedEnd}ms from end)`);\n        \n        return trimmedBuffer;\n    }\n    \n    audioBufferToWav(buffer) {\n        // Target settings for 256kbps (approximately)\n        const targetSampleRate = 16000; // 16kHz for speech quality\n        const targetChannels = 1;       // Mono for speech\n        const bitsPerSample = 16;\n        \n        // Resample and convert to mono if needed\n        const resampledBuffer = this.resampleAndConvertToMono(buffer, targetSampleRate);\n        \n        const length = resampledBuffer.length;\n        const numberOfChannels = targetChannels;\n        const sampleRate = targetSampleRate;\n        const bytesPerSample = bitsPerSample / 8;\n        const blockAlign = numberOfChannels * bytesPerSample;\n        const byteRate = sampleRate * blockAlign; // This will be 32000 bytes/sec = 256kbps\n        const dataSize = length * blockAlign;\n        const bufferSize = 44 + dataSize;\n        \n        const arrayBuffer = new ArrayBuffer(bufferSize);\n        const view = new DataView(arrayBuffer);\n        \n        // Write WAV header\n        const writeString = (offset, string) => {\n            for (let i = 0; i < string.length; i++) {\n                view.setUint8(offset + i, string.charCodeAt(i));\n            }\n        };\n        \n        // RIFF header\n        writeString(0, 'RIFF');\n        view.setUint32(4, bufferSize - 8, true);\n        writeString(8, 'WAVE');\n        \n        // Format chunk\n        writeString(12, 'fmt ');\n        view.setUint32(16, 16, true); // Subchunk1Size\n        view.setUint16(20, 1, true); // AudioFormat (PCM)\n        view.setUint16(22, numberOfChannels, true);\n        view.setUint32(24, sampleRate, true);\n        view.setUint32(28, byteRate, true);\n        view.setUint16(32, blockAlign, true);\n        view.setUint16(34, bitsPerSample, true);\n        \n        // Data chunk\n        writeString(36, 'data');\n        view.setUint32(40, dataSize, true);\n        \n        // Write audio data (mono)\n        let offset = 44;\n        for (let i = 0; i < length; i++) {\n            const sample = Math.max(-1, Math.min(1, resampledBuffer[i]));\n            const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n            view.setInt16(offset, intSample, true);\n            offset += 2;\n        }\n        \n        return new Blob([arrayBuffer], { type: 'audio/wav' });\n    }\n    \n    resampleAndConvertToMono(buffer, targetSampleRate) {\n        const originalSampleRate = buffer.sampleRate;\n        const originalLength = buffer.length;\n        const numberOfChannels = buffer.numberOfChannels;\n        \n        // Calculate new length after resampling\n        const resampleRatio = targetSampleRate / originalSampleRate;\n        const newLength = Math.floor(originalLength * resampleRatio);\n        \n        // Create output array\n        const resampledData = new Float32Array(newLength);\n        \n        // Convert to mono first (mix all channels)\n        const monoData = new Float32Array(originalLength);\n        for (let i = 0; i < originalLength; i++) {\n            let sum = 0;\n            for (let channel = 0; channel < numberOfChannels; channel++) {\n                sum += buffer.getChannelData(channel)[i];\n            }\n            monoData[i] = sum / numberOfChannels; // Average all channels\n        }\n        \n        // Resample using linear interpolation\n        for (let i = 0; i < newLength; i++) {\n            const originalIndex = i / resampleRatio;\n            const index = Math.floor(originalIndex);\n            const fraction = originalIndex - index;\n            \n            if (index + 1 < originalLength) {\n                // Linear interpolation between two samples\n                resampledData[i] = monoData[index] * (1 - fraction) + monoData[index + 1] * fraction;\n            } else {\n                // Use the last sample if we're at the end\n                resampledData[i] = monoData[index] || 0;\n            }\n        }\n        \n        return resampledData;\n    }\n    \n    async submitForGrading(audioBlob) {\n        try {\n            // Show loading state\n            this.showLoadingState();\n            \n            // Get current practice parameters\n            console.log(topicItemData);\n            const params = {\n                topic_id: topicItemData.topic_id || '', // topicItemData is global variable\n                item_id: topicItemData.item_id || '',\n                item_type: topicItemData.item_type || 'question'\n            };\n            \n            // Submit to pronunciation service\n            const result = await this.pronunciationService.gradeRecording(audioBlob, params);\n            \n            // Display results\n            this.displayResults(result);\n            \n        } catch (error) {\n            console.error('Error submitting recording:', error);\n            this.showError(error.message || 'Failed to submit recording for grading');\n        } finally {\n            this.hideLoadingState();\n        }\n    }\n    \n    displayResults(data) {\n        // Extract real data from Azure pronunciation assessment response\n        if (!data || !data.analysis) {\n            this.showError('Invalid pronunciation analysis data received');\n            return;\n        }\n\n        const analysis = data.analysis;\n        const scores = analysis.scores || {};\n        const itemData = data.item_data || {};\n        \n        // Extract scores with fallback values\n        const overallScore = Math.round(analysis.overall_score || 0);\n        const accuracy = Math.round(scores.accuracy || 0);\n        const fluency = Math.round(scores.fluency || 0);\n        const completeness = Math.round(scores.completeness || 0);\n        const pronunciation = Math.round(scores.pronunciation || 0);\n        \n        // Extract text data\n        const expectedText = data.expected_text || itemData.word || 'Unknown';\n        const recognizedText = analysis.recognized_text || 'Not recognized';\n        const feedback = analysis.feedback || 'No feedback available';\n        \n        // Extract additional information\n        const detectedIssues = analysis.detected_issues || [];\n        const suggestions = analysis.suggestions || [];\n        \n        // Build issues and suggestions HTML\n        let issuesHtml = '';\n        if (detectedIssues.length > 0) {\n            issuesHtml = `\n                <div class=\"detected-issues\">\n                    <h4>Detected Issues:</h4>\n                    <ul>\n                        ${detectedIssues.map(issue => `<li>${issue}</li>`).join('')}\n                    </ul>\n                </div>\n            `;\n        }\n        \n        let suggestionsHtml = '';\n        if (suggestions.length > 0) {\n            suggestionsHtml = `\n                <div class=\"suggestions\">\n                    <h4>Suggestions for Improvement:</h4>\n                    <ul>\n                        ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}\n                    </ul>\n                </div>\n            `;\n        }\n        \n        // Update practice result section\n        const practiceResult = document.querySelector('.practice-result');\n        if (practiceResult) {\n            practiceResult.innerHTML = `\n                <div class=\"result-card\">\n                    <h3>Pronunciation Analysis</h3>\n                    \n                    <div class=\"text-comparison\">\n                        <div class=\"expected-text\">\n                            <span class=\"text-label\">Expected:</span>\n                            <span class=\"text-value\">\"${expectedText}\"</span>\n                        </div>\n                        <div class=\"recognized-text\">\n                            <span class=\"text-label\">You said:</span>\n                            <span class=\"text-value\">\"${recognizedText}\"</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"overall-score\">\n                        <div class=\"circular-progress ${this.getScoreClass(overallScore)}\">\n                            <div class=\"circular-progress-inner\">\n                                <div class=\"circular-progress-circle\">\n                                    <div class=\"circular-progress-mask full\">\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(${overallScore * 3.6}deg)\"></div>\n                                    </div>\n                                    <div class=\"circular-progress-mask\">\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(${Math.min(180, overallScore * 3.6)}deg)\"></div>\n                                    </div>\n                                    <div class=\"circular-progress-inside\">\n                                        <span class=\"circular-progress-percentage\">${overallScore}<span class=\"percentage-sign\">%</span></span>\n                                        <span class=\"circular-progress-label\">Overall Score</span>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <div class=\"detailed-metrics\">\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Accuracy:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ${this.getScoreClass(accuracy)}\" style=\"width: ${accuracy}%\"></div>\n                            </div>\n                            <span class=\"metric-value\">${accuracy}%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Fluency:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ${this.getScoreClass(fluency)}\" style=\"width: ${fluency}%\"></div>\n                            </div>\n                            <span class=\"metric-value\">${fluency}%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Completeness:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ${this.getScoreClass(completeness)}\" style=\"width: ${completeness}%\"></div>\n                            </div>\n                            <span class=\"metric-value\">${completeness}%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Pronunciation:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ${this.getScoreClass(pronunciation)}\" style=\"width: ${pronunciation}%\"></div>\n                            </div>\n                            <span class=\"metric-value\">${pronunciation}%</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"feedback-text\">\n                        <h4>Feedback:</h4>\n                        <p>${feedback}</p>\n                    </div>\n                    \n                    ${issuesHtml}\n                    ${suggestionsHtml}\n                    \n                    <div class=\"result-actions\">\n                        <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\n                        <button class=\"btn btn-secondary\" onclick=\"window.history.back()\">Back to Topics</button>\n                    </div>\n                </div>\n            `;\n        }\n        \n        console.log('Pronunciation results displayed:', {\n            overallScore,\n            scores: { accuracy, fluency, completeness, pronunciation },\n            expectedText,\n            recognizedText,\n            feedback,\n            detectedIssues,\n            suggestions\n        });\n    }\n    \n    getScoreClass(score) {\n        if (score >= 80) return 'score-excellent';\n        if (score >= 60) return 'score-good';\n        if (score >= 40) return 'score-fair';\n        return 'score-poor';\n    }\n    \n    showRecordingElements() {\n        if (this.recordingStatus) {\n            this.recordingStatus.style.display = 'block';\n        }\n        if (this.recordingDuration) {\n            this.recordingDuration.style.display = 'flex';\n        }\n    }\n    \n    hideRecordingElements() {\n        if (this.recordingStatus) {\n            this.recordingStatus.style.display = 'none';\n        }\n        if (this.recordingDuration) {\n            this.recordingDuration.style.display = 'none';\n        }\n    }\n    \n    updateButtonState() {\n        if (this.recordBtn) {\n            const recordText = this.recordBtn.querySelector('.record-text');\n            if (this.isRecording) {\n                this.recordBtn.classList.add('recording');\n                if (recordText) recordText.textContent = 'Stop Recording';\n            } else {\n                this.recordBtn.classList.remove('recording');\n                if (recordText) recordText.textContent = 'Record now';\n            }\n        }\n    }\n    \n    startTimer() {\n        this.timerInterval = setInterval(() => {\n            if (this.startTime && this.durationText) {\n                const elapsed = Date.now() - this.startTime;\n                const minutes = Math.floor(elapsed / 60000);\n                const seconds = Math.floor((elapsed % 60000) / 1000);\n                \n                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n                this.durationText.textContent = timeString;\n            }\n        }, 100);\n    }\n    \n    stopTimer() {\n        if (this.timerInterval) {\n            clearInterval(this.timerInterval);\n            this.timerInterval = null;\n        }\n    }\n    \n    showLoadingState() {\n        if (this.recordBtn) {\n            this.recordBtn.disabled = true;\n            const recordText = this.recordBtn.querySelector('.record-text');\n            if (recordText) recordText.textContent = 'Processing...';\n        }\n    }\n    \n    hideLoadingState() {\n        if (this.recordBtn) {\n            this.recordBtn.disabled = false;\n            this.updateButtonState();\n        }\n    }\n    \n    showError(message) {\n        console.error('PronunciationRecorder Error:', message);\n        \n        // You can implement a more sophisticated error display here\n        const practiceResult = document.querySelector('.practice-result');\n        if (practiceResult) {\n            practiceResult.innerHTML = `\n                <div class=\"error-message\">\n                    <h3>Error</h3>\n                    <p>${message}</p>\n                    <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\n                </div>\n            `;\n        }\n    }\n    \n    resetRecording() {\n        // Reset the recording state\n        this.isRecording = false;\n        this.audioChunks = [];\n        this.startTime = null;\n        \n        // Clear any existing intervals\n        this.stopTimer();\n        \n        // Reset UI\n        this.hideRecordingElements();\n        this.updateButtonState();\n        \n        // Clear results\n        const practiceResult = document.querySelector('.practice-result');\n        if (practiceResult) {\n            practiceResult.innerHTML = '';\n        }\n        \n        // Reset duration text\n        if (this.durationText) {\n            this.durationText.textContent = '00:00';\n        }\n    }\n    \n    getNonce() {\n        // Get WordPress nonce if available\n        return window.toeic_ajax_nonce || '';\n    }\n\n    handleSuggestion(e) {\n        const prompt = e.target.dataset.prompt;\n        const subject = this.paragraphText;\n        this.getSuggestion({prompt, subject});\n    }\n\n    async getSuggestion({prompt, subject}) {\n        try {\n            const response = await this.pronunciationService.getSuggestions({prompt, subject});\n            console.log(response);\n        } catch (error) {\n            console.error('Error fetching suggestions:', error);\n            throw error;\n        }\n    }\n}\n\nexport {PronunciationRecorder};\n", "/**\r\n * TOEIC Test Detail Page\r\n * \r\n * Handles the test-taking functionality including:\r\n * 1. Timer countdown with progress bar\r\n * 2. Question navigation and display\r\n * 3. Answer selection and submission\r\n */\r\n\r\nimport Loader from '../components/Loader';\r\nimport testService from '../services/TestService';\r\nimport { QuestionRendererFactory } from '../components/questions';\r\n\r\nclass TestDetailPage {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {HTMLElement} element - The test detail page container element\r\n     */\r\n    constructor(element) {\r\n        // Main container and data\r\n        this.element = element;\r\n        this.testData = window.toeicTestData || {};\r\n        this.questions = [];\r\n        this.sections = this.testData.sections || [];\r\n        this.currentQuestionIndex = 0;\r\n        this.userAnswers = {};\r\n        this.isTestActive = false;\r\n        this.isReviewMode = false;\r\n        \r\n        // Timer elements\r\n        this.timerBar = document.getElementById('toeic-timer-bar');\r\n        this.timerDisplay = document.getElementById('toeic-timer-display');\r\n        this.timerMinutes = document.getElementById('toeic-timer-minutes');\r\n        this.timerSeconds = document.getElementById('toeic-timer-seconds');\r\n        \r\n        // Question elements\r\n        this.questionContainer = document.getElementById('toeic-question-container');\r\n        this.sectionInfo = document.getElementById('toeic-section-info');\r\n        this.sectionTitle = document.getElementById('toeic-section-title');\r\n        this.sectionInstructions = document.getElementById('toeic-section-instructions');\r\n        this.questionNumber = document.getElementById('toeic-question-number');\r\n        this.questionText = document.getElementById('toeic-question-text');\r\n        this.questionOptions = document.getElementById('toeic-question-options');\r\n        \r\n        // Navigation elements\r\n        this.prevButton = document.getElementById('toeic-prev-btn');\r\n        this.nextButton = document.getElementById('toeic-next-btn');\r\n        this.submitButton = document.getElementById('toeic-submit-btn');\r\n        this.questionIndicators = document.getElementById('toeic-question-indicators');\r\n        this.currentQuestionDisplay = document.getElementById('toeic-current-question');\r\n        this.totalQuestionsDisplay = document.getElementById('toeic-total-questions');\r\n        \r\n        // Results elements\r\n        this.resultsContainer = document.getElementById('toeic-results-container');\r\n        this.resultsContent = document.getElementById('toeic-results-content');\r\n        this.reviewButton = document.getElementById('toeic-review-btn');\r\n        \r\n        // Timer variables\r\n        this.timeLimit = this.testData.time_limit || 3600; // Default 60 minutes\r\n        this.timeRemaining = this.timeLimit;\r\n        this.timerInterval = null;\r\n        \r\n        // Create loader\r\n        this.loader = Loader.forElement(this.element, {\r\n            text: 'Loading test data...'\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Initialize the test page\r\n     */\r\n    init() {\r\n        // Load questions\r\n        this.loadQuestions();\r\n        \r\n        // Set up event listeners\r\n        this.setupEventListeners();\r\n    }\r\n    \r\n    /**\r\n     * Load questions from the server\r\n     */\r\n    loadQuestions() {\r\n        if (!this.testData.id) {\r\n            console.error('No test ID provided');\r\n            return;\r\n        }\r\n        \r\n        this.loader.show('Loading test questions...');\r\n        \r\n        testService.getTestQuestions(this.testData.id)\r\n            .then(data => {\r\n                this.questions = data.questions || [];\r\n                this.renderQuestionIndicators();\r\n                this.startTest();\r\n            })\r\n            .catch(error => {\r\n                console.error('Error loading questions:', error);\r\n                alert('Failed to load test questions. Please try again.');\r\n            })\r\n            .finally(() => {\r\n                this.loader.hide();\r\n            });\r\n    }\r\n    \r\n    /**\r\n     * Set up event listeners\r\n     */\r\n    setupEventListeners() {\r\n        // Navigation buttons\r\n        if (this.prevButton) {\r\n            this.prevButton.addEventListener('click', this.goToPreviousQuestion.bind(this));\r\n        }\r\n        \r\n        if (this.nextButton) {\r\n            this.nextButton.addEventListener('click', this.goToNextQuestion.bind(this));\r\n        }\r\n        \r\n        if (this.submitButton) {\r\n            this.submitButton.addEventListener('click', this.confirmSubmitTest.bind(this));\r\n        }\r\n        \r\n        if (this.reviewButton) {\r\n            this.reviewButton.addEventListener('click', this.startReviewMode.bind(this));\r\n        }\r\n        \r\n        // Handle beforeunload event to warn user before leaving\r\n        window.addEventListener('beforeunload', (event) => {\r\n            if (this.isTestActive && !this.isReviewMode) {\r\n                const message = 'You are in the middle of a test. Are you sure you want to leave?';\r\n                event.returnValue = message;\r\n                return message;\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Start the test\r\n     */\r\n    startTest() {\r\n        if (!this.questions.length) {\r\n            alert('No questions available for this test.');\r\n            return;\r\n        }\r\n        \r\n        this.isTestActive = true;\r\n        this.currentQuestionIndex = 0;\r\n        this.userAnswers = {};\r\n        \r\n        // Start the timer\r\n        this.startTimer();\r\n        \r\n        // Show the first question\r\n        this.showCurrentQuestion();\r\n    }\r\n    \r\n    /**\r\n     * Start the timer\r\n     */\r\n    startTimer() {\r\n        // Reset timer\r\n        this.timeRemaining = this.timeLimit;\r\n        this.updateTimerDisplay();\r\n        \r\n        // Clear any existing interval\r\n        if (this.timerInterval) {\r\n            clearInterval(this.timerInterval);\r\n        }\r\n        \r\n        // Start new interval\r\n        const startTime = Date.now();\r\n        this.timerInterval = setInterval(() => {\r\n            const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);\r\n            this.timeRemaining = Math.max(0, this.timeLimit - elapsedSeconds);\r\n            \r\n            this.updateTimerDisplay();\r\n            \r\n            // If time is up, auto-submit the test\r\n            if (this.timeRemaining <= 0) {\r\n                clearInterval(this.timerInterval);\r\n                this.submitTest();\r\n            }\r\n        }, 1000);\r\n    }\r\n    \r\n    /**\r\n     * Update the timer display\r\n     */\r\n    updateTimerDisplay() {\r\n        // Calculate minutes and seconds\r\n        const minutes = Math.floor(this.timeRemaining / 60);\r\n        const seconds = this.timeRemaining % 60;\r\n        \r\n        // Update text display\r\n        if (this.timerMinutes) {\r\n            this.timerMinutes.textContent = minutes.toString().padStart(2, '0');\r\n        }\r\n        \r\n        if (this.timerSeconds) {\r\n            this.timerSeconds.textContent = seconds.toString().padStart(2, '0');\r\n        }\r\n        \r\n        // Update progress bar\r\n        if (this.timerBar) {\r\n            const percentRemaining = (this.timeRemaining / this.timeLimit) * 100;\r\n            this.timerBar.style.width = `${percentRemaining}%`;\r\n            \r\n            // Change color based on time remaining\r\n            if (percentRemaining < 20) {\r\n                this.timerBar.style.backgroundColor = '#ff4d4d'; // Red\r\n            } else if (percentRemaining < 50) {\r\n                this.timerBar.style.backgroundColor = '#ffa64d'; // Orange\r\n            } else {\r\n                this.timerBar.style.backgroundColor = '#4CAF50'; // Green\r\n            }\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Render question indicators for navigation\r\n     */\r\n    renderQuestionIndicators() {\r\n        if (!this.questionIndicators || !this.questions.length) return;\r\n        \r\n        this.questionIndicators.innerHTML = '';\r\n        \r\n        this.questions.forEach((_, index) => {\r\n            const indicator = document.createElement('div');\r\n            indicator.className = 'toeic-question-indicator';\r\n            indicator.dataset.index = index;\r\n            indicator.addEventListener('click', () => this.goToQuestion(index));\r\n            \r\n            this.questionIndicators.appendChild(indicator);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Update question indicators based on answered status\r\n     */\r\n    updateQuestionIndicators() {\r\n        if (!this.questionIndicators) return;\r\n        \r\n        const indicators = this.questionIndicators.querySelectorAll('.toeic-question-indicator');\r\n        \r\n        indicators.forEach((indicator, index) => {\r\n            // Remove all classes first\r\n            indicator.classList.remove('current', 'answered', 'unanswered');\r\n            \r\n            // Add appropriate class\r\n            if (index === this.currentQuestionIndex) {\r\n                indicator.classList.add('current');\r\n            }\r\n            \r\n            const questionId = this.questions[index]?.id;\r\n            if (questionId && this.userAnswers[questionId]) {\r\n                indicator.classList.add('answered');\r\n            } else {\r\n                indicator.classList.add('unanswered');\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Show the current question\r\n     */\r\n    showCurrentQuestion() {\r\n        if (!this.questions.length) return;\r\n        \r\n        const question = this.questions[this.currentQuestionIndex];\r\n        if (!question) return;\r\n        \r\n        // Update question number display\r\n        if (this.currentQuestionDisplay) {\r\n            this.currentQuestionDisplay.textContent = (this.currentQuestionIndex + 1).toString();\r\n        }\r\n        \r\n        if (this.totalQuestionsDisplay) {\r\n            this.totalQuestionsDisplay.textContent = this.questions.length.toString();\r\n        }\r\n        \r\n        // Find section for this question\r\n        const section = this.sections.find(s => s.id === question.section_id);\r\n        \r\n        // Update section info if it's available and different from current\r\n        if (section) {\r\n            if (this.sectionTitle) {\r\n                this.sectionTitle.textContent = section.title || '';\r\n            }\r\n            \r\n            if (this.sectionInstructions) {\r\n                this.sectionInstructions.innerHTML = section.instructions || '';\r\n            }\r\n        }\r\n        \r\n        // Update question display\r\n        if (this.questionNumber) {\r\n            this.questionNumber.textContent = `Question ${this.currentQuestionIndex + 1}`;\r\n        }\r\n        \r\n        if (this.questionText) {\r\n            this.questionText.innerHTML = question.content || '';\r\n        }\r\n        \r\n        // Render question options\r\n        this.renderQuestionOptions(question);\r\n        \r\n        // Update navigation buttons\r\n        this.updateNavigationButtons();\r\n        \r\n        // Update question indicators\r\n        this.updateQuestionIndicators();\r\n    }\r\n    \r\n    /**\r\n     * Render options for the current question\r\n     * \r\n     * @param {Object} question - The question object\r\n     */\r\n    renderQuestionOptions(question) {\r\n        if (!this.questionOptions || !question) return;\r\n        \r\n        this.questionOptions.innerHTML = '';\r\n        \r\n        try {\r\n            // Use the factory to create the appropriate renderer\r\n            const renderer = QuestionRendererFactory.createRenderer(\r\n                question, \r\n                this.questionOptions, \r\n                {\r\n                    isReviewMode: this.isReviewMode,\r\n                    userAnswers: this.userAnswers,\r\n                    onAnswerSelected: this.saveAnswer.bind(this)\r\n                }\r\n            );\r\n            \r\n            // Render the question\r\n            renderer.render();\r\n        } catch (error) {\r\n            console.error('Error rendering question:', error);\r\n            \r\n            // Fallback for unsupported question types\r\n            this.renderFallbackQuestion(question);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Fallback renderer for unsupported question types\r\n     * \r\n     * @param {Object} question - The question object\r\n     */\r\n    renderFallbackQuestion(question) {\r\n        const fallbackElement = document.createElement('div');\r\n        fallbackElement.className = 'toeic-question-fallback';\r\n        fallbackElement.innerHTML = `\r\n            <p class=\"toeic-question-unsupported\">\r\n                This question type (${question.type || 'unknown'}) is not supported in the current view.\r\n            </p>\r\n        `;\r\n        \r\n        this.questionOptions.appendChild(fallbackElement);\r\n    }\r\n    \r\n    /**\r\n     * Save the user's answer for a question\r\n     * \r\n     * @param {number|string} questionId - The question ID\r\n     * @param {string} answer - The selected answer\r\n     */\r\n    saveAnswer(questionId, answer) {\r\n        this.userAnswers[questionId] = answer;\r\n        this.updateQuestionIndicators();\r\n    }\r\n    \r\n    /**\r\n     * Update navigation buttons based on current question\r\n     */\r\n    updateNavigationButtons() {\r\n        // Previous button\r\n        if (this.prevButton) {\r\n            this.prevButton.disabled = this.currentQuestionIndex === 0;\r\n        }\r\n        \r\n        // Next button\r\n        if (this.nextButton) {\r\n            const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;\r\n            this.nextButton.style.display = isLastQuestion ? 'none' : 'inline-block';\r\n        }\r\n        \r\n        // Submit button is always visible with the new UI\r\n        // No need to toggle visibility based on question index\r\n    }\r\n    \r\n    /**\r\n     * Go to a specific question\r\n     * \r\n     * @param {number} index - Question index to navigate to\r\n     */\r\n    goToQuestion(index) {\r\n        if (index < 0 || index >= this.questions.length) return;\r\n        \r\n        this.currentQuestionIndex = index;\r\n        this.showCurrentQuestion();\r\n    }\r\n    \r\n    /**\r\n     * Go to the previous question\r\n     */\r\n    goToPreviousQuestion() {\r\n        if (this.currentQuestionIndex > 0) {\r\n            this.currentQuestionIndex--;\r\n            this.showCurrentQuestion();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Go to the next question\r\n     */\r\n    goToNextQuestion() {\r\n        if (this.currentQuestionIndex < this.questions.length - 1) {\r\n            this.currentQuestionIndex++;\r\n            this.showCurrentQuestion();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Confirm before submitting the test\r\n     */\r\n    confirmSubmitTest() {\r\n        // Count unanswered questions\r\n        const answeredCount = Object.keys(this.userAnswers).length;\r\n        const unansweredCount = this.questions.length - answeredCount;\r\n        \r\n        let message = 'Are you sure you want to submit your test?';\r\n        \r\n        if (unansweredCount > 0) {\r\n            message = `You have ${unansweredCount} unanswered question${unansweredCount > 1 ? 's' : ''}. Are you sure you want to submit your test?`;\r\n        }\r\n        \r\n        if (confirm(message)) {\r\n            this.submitTest();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Submit the test\r\n     */\r\n    submitTest() {\r\n        // Stop the timer\r\n        if (this.timerInterval) {\r\n            clearInterval(this.timerInterval);\r\n        }\r\n        \r\n        this.isTestActive = false;\r\n        \r\n        // Show loader\r\n        this.loader.show('Submitting your test...');\r\n        \r\n        // Prepare data for submission\r\n        const data = {\r\n            test_id: this.testData.id,\r\n            time_taken: this.timeLimit - this.timeRemaining,\r\n            answers: this.userAnswers\r\n        };\r\n        \r\n        // Submit to server\r\n        testService.submitTest(this.testData.id, this.userAnswers, this.timeLimit - this.timeRemaining)\r\n            .then(response => {\r\n                this.showResults(response);\r\n            })\r\n            .catch(error => {\r\n                console.error('Error submitting test:', error);\r\n                alert('Failed to submit your test. Please try again.');\r\n            })\r\n            .finally(() => {\r\n                this.loader.hide();\r\n            });\r\n    }\r\n    \r\n    /**\r\n     * Show test results\r\n     * \r\n     * @param {Object} results - Test results from server\r\n     */\r\n    showResults(results) {\r\n        // Hide question container\r\n        if (this.questionContainer) {\r\n            this.questionContainer.style.display = 'none';\r\n        }\r\n        \r\n        // Show results container\r\n        if (this.resultsContainer) {\r\n            this.resultsContainer.style.display = 'block';\r\n        }\r\n        \r\n        // Hide navigation\r\n        const navigationElement = document.querySelector('.toeic-test-navigation');\r\n        if (navigationElement) {\r\n            navigationElement.style.display = 'none';\r\n        }\r\n        \r\n        // Render results content\r\n        if (this.resultsContent) {\r\n            const score = results.score || 0;\r\n            const totalPoints = results.total_points || this.questions.length;\r\n            const percentage = Math.round((score / totalPoints) * 100);\r\n            \r\n            let resultClass = 'average';\r\n            if (percentage >= 80) {\r\n                resultClass = 'excellent';\r\n            } else if (percentage >= 60) {\r\n                resultClass = 'good';\r\n            } else if (percentage < 40) {\r\n                resultClass = 'poor';\r\n            }\r\n            \r\n            this.resultsContent.innerHTML = `\r\n                <div class=\"toeic-score ${resultClass}\">\r\n                    <div class=\"toeic-score-number\">${score}/${totalPoints}</div>\r\n                    <div class=\"toeic-score-percentage\">${percentage}%</div>\r\n                </div>\r\n                \r\n                <div class=\"toeic-score-breakdown\">\r\n                    <h3>Score Breakdown</h3>\r\n                    <table class=\"toeic-score-table\">\r\n                        <thead>\r\n                            <tr>\r\n                                <th>Section</th>\r\n                                <th>Score</th>\r\n                                <th>Out of</th>\r\n                                <th>Percentage</th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                            ${this.renderSectionScores(results.section_scores || [])}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n                \r\n                <div class=\"toeic-feedback\">\r\n                    <h3>Feedback</h3>\r\n                    <p>${results.feedback || 'No feedback available.'}</p>\r\n                </div>\r\n            `;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Render section scores for the results table\r\n     * \r\n     * @param {Array} sectionScores - Array of section score objects\r\n     * @returns {string} HTML for the section scores table rows\r\n     */\r\n    renderSectionScores(sectionScores) {\r\n        if (!sectionScores || !sectionScores.length) {\r\n            return '<tr><td colspan=\"4\">No section scores available</td></tr>';\r\n        }\r\n        \r\n        return sectionScores.map(section => {\r\n            const percentage = Math.round((section.score / section.total) * 100);\r\n            return `\r\n                <tr>\r\n                    <td>${section.title}</td>\r\n                    <td>${section.score}</td>\r\n                    <td>${section.total}</td>\r\n                    <td>${percentage}%</td>\r\n                </tr>\r\n            `;\r\n        }).join('');\r\n    }\r\n    \r\n    /**\r\n     * Start review mode to review answers\r\n     */\r\n    startReviewMode() {\r\n        this.isReviewMode = true;\r\n        \r\n        // Show question container again\r\n        if (this.questionContainer) {\r\n            this.questionContainer.style.display = 'block';\r\n        }\r\n        \r\n        // Show navigation again but with modified buttons\r\n        const navigationElement = document.querySelector('.toeic-test-navigation');\r\n        if (navigationElement) {\r\n            navigationElement.style.display = 'block';\r\n            \r\n            // Hide submit button\r\n            if (this.submitButton) {\r\n                this.submitButton.style.display = 'none';\r\n            }\r\n            \r\n            // Show next button for all questions\r\n            if (this.nextButton) {\r\n                this.nextButton.style.display = 'inline-block';\r\n            }\r\n        }\r\n        \r\n        // Hide results\r\n        if (this.resultsContainer) {\r\n            this.resultsContainer.style.display = 'none';\r\n        }\r\n        \r\n        // Go to first question\r\n        this.currentQuestionIndex = 0;\r\n        this.showCurrentQuestion();\r\n    }\r\n}\r\n\r\n// Initialize the page when DOM is loaded\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n    const testDetailElement = document.getElementById('toeic-test-detail');\r\n    if (testDetailElement) {\r\n        const testDetailPage = new TestDetailPage(testDetailElement);\r\n        testDetailPage.init();\r\n    }\r\n});\r\n\r\nexport default TestDetailPage;", "/**\r\n * TOEIC Practice Plugin - Ajax Service\r\n * \r\n * A service class that provides a secure interface for making AJAX requests\r\n * to WordPress backend, handling nonces and other security aspects.\r\n */\r\nclass AjaxService {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.ajaxUrl - WordPress AJAX URL (default: from global toeicPracticeAjax)\r\n     * @param {string} options.nonce - Security nonce (default: from global toeicPracticeAjax)\r\n     * @param {string} options.nonceParam - Name of nonce parameter (default: 'nonce')\r\n     * @param {Function} options.onError - Global error handler (optional)\r\n     */\r\n    constructor(options = {}) {\r\n        // Check if global toeicPracticeAjax is available\r\n        const globalData = window.toeicPracticeAjax || {};\r\n        \r\n        this.ajaxUrl = options.ajaxUrl || globalData.ajaxUrl || '/wp-admin/admin-ajax.php';\r\n        this.nonce = options.nonce || globalData.nonce || '';\r\n        this.nonceParam = options.nonceParam || 'nonce';\r\n        this.onError = options.onError || null;\r\n        \r\n        // Validate required parameters\r\n        if (!this.ajaxUrl) {\r\n            console.error('AjaxService: ajaxUrl is required');\r\n        }\r\n        \r\n        if (!this.nonce) {\r\n            console.warn('AjaxService: nonce is not provided, requests may fail security checks');\r\n        }\r\n        \r\n        // Keep track of pending requests\r\n        this.pendingRequests = new Map();\r\n    }\r\n    \r\n    /**\r\n     * Make a GET request\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} params - Additional parameters (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    get(action, params = {}, options = {}) {\r\n        const requestParams = {\r\n            action,\r\n            [this.nonceParam]: this.nonce,\r\n            ...params\r\n        };\r\n        \r\n        const queryString = new URLSearchParams(requestParams).toString();\r\n        const url = `${this.ajaxUrl}?${queryString}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'GET',\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    post(action, data = {}, options = {}) {\r\n        const formData = new FormData();\r\n        \r\n        // Add action and nonce\r\n        formData.append('action', action);\r\n        formData.append(this.nonceParam, this.nonce);\r\n        \r\n        // Add other data\r\n        Object.entries(data).forEach(([key, value]) => {\r\n            // Handle Blob objects (including File and audio blobs)\r\n            if (value instanceof Blob) {\r\n                formData.append(key, value, value.name || `${key}.blob`);\r\n            }\r\n            // Handle arrays and other objects (but not Blobs)\r\n            else if (typeof value === 'object' && value !== null) {\r\n                formData.append(key, JSON.stringify(value));\r\n            } else {\r\n                formData.append(key, value);\r\n            }\r\n        });\r\n        \r\n        return this._makeRequest(this.ajaxUrl, {\r\n            method: 'POST',\r\n            body: formData,\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request with JSON data\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    postJson(action, data = {}, options = {}) {\r\n        const requestData = {\r\n            action,\r\n            [this.nonceParam]: this.nonce,\r\n            ...data\r\n        };\r\n        \r\n        return this._makeRequest(this.ajaxUrl, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify(requestData),\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a request with the fetch API\r\n     * \r\n     * @param {string} url - Request URL\r\n     * @param {Object} options - Request options\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     * @private\r\n     */\r\n    _makeRequest(url, options) {\r\n        // Generate a unique request ID\r\n        const requestId = Math.random().toString(36).substring(2, 15);\r\n        \r\n        // Create abort controller for timeout\r\n        const controller = new AbortController();\r\n        const { signal } = controller;\r\n        \r\n        // Set timeout if specified\r\n        const timeout = options.timeout || 30000; // Default 30 seconds\r\n        const timeoutId = setTimeout(() => {\r\n            controller.abort();\r\n        }, timeout);\r\n        \r\n        // Store the request in pending requests\r\n        this.pendingRequests.set(requestId, { controller });\r\n        \r\n        // Show loader if specified\r\n        const loader = options.loader || (window.toeicLoader || null);\r\n        const showLoader = options.showLoader !== false;\r\n        \r\n        if (loader && showLoader) {\r\n            loader.show(options.loaderText || 'Loading...');\r\n        }\r\n        \r\n        // Make the request\r\n        return fetch(url, {\r\n            ...options,\r\n            signal,\r\n            credentials: 'same-origin'\r\n        })\r\n        .then(response => {\r\n            // Clear timeout\r\n            clearTimeout(timeoutId);\r\n            \r\n            // Check if response is OK\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);\r\n            }\r\n            \r\n            // Parse response based on content type\r\n            const contentType = response.headers.get('content-type');\r\n            if (contentType && contentType.includes('application/json')) {\r\n                return response.json();\r\n            }\r\n            \r\n            return response.text();\r\n        })\r\n        .then(data => {\r\n            // Handle WordPress AJAX response format\r\n            if (typeof data === 'object' && data !== null) {\r\n                // Check for WordPress error response\r\n                if (data.success === false) {\r\n                    throw new Error(data.data || 'Unknown error');\r\n                }\r\n                \r\n                // Return data or data.data if available\r\n                return data.data !== undefined ? data.data : data;\r\n            }\r\n            \r\n            return data;\r\n        })\r\n        .catch(error => {\r\n            // Handle aborted requests\r\n            if (error.name === 'AbortError') {\r\n                throw new Error('Request timed out');\r\n            }\r\n            \r\n            // Call global error handler if available\r\n            if (this.onError) {\r\n                this.onError(error);\r\n            }\r\n            \r\n            throw error;\r\n        })\r\n        .finally(() => {\r\n            // Remove from pending requests\r\n            this.pendingRequests.delete(requestId);\r\n            \r\n            // Hide loader\r\n            if (loader && showLoader) {\r\n                // Only hide if no other requests are pending with this loader\r\n                const otherRequestsWithLoader = Array.from(this.pendingRequests.values())\r\n                    .some(req => req.loader === loader);\r\n                \r\n                if (!otherRequestsWithLoader) {\r\n                    loader.hide();\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Cancel all pending requests\r\n     */\r\n    cancelAll() {\r\n        this.pendingRequests.forEach(request => {\r\n            request.controller.abort();\r\n        });\r\n        \r\n        this.pendingRequests.clear();\r\n    }\r\n    \r\n    /**\r\n     * Update the nonce\r\n     * \r\n     * @param {string} nonce - New nonce value\r\n     */\r\n    updateNonce(nonce) {\r\n        this.nonce = nonce;\r\n    }\r\n    \r\n    /**\r\n     * Create a new instance with custom settings\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @returns {AjaxService} - New AjaxService instance\r\n     */\r\n    static create(options = {}) {\r\n        return new AjaxService(options);\r\n    }\r\n}\r\n\r\n// Create and export default instance\r\nconst ajaxService = new AjaxService();\r\n\r\nexport default ajaxService;\r\n\r\n// Also export the class for creating custom instances\r\nexport { AjaxService };\r\n", "/**\r\n * Pronunciation Service\r\n * Handles pronunciation grading and related API calls\r\n */\r\nimport ajaxService from './AjaxService.js';\r\n\r\nclass PronunciationService {\r\n    constructor() {\r\n        this.ajaxService = ajaxService;\r\n    }\r\n\r\n    /**\r\n     * Submit audio recording for pronunciation grading\r\n     * @param {Blob} audioBlob - The recorded audio blob\r\n     * @param {Object} params - Practice parameters\r\n     * @returns {Promise<Object>} - Grading results\r\n     */\r\n    async gradeRecording(audioBlob, params = {}) {\r\n        try {\r\n            // Create data object with audio file and parameters\r\n            const data = {\r\n                audio: audioBlob,\r\n                topic_id: params.topic_id || '',\r\n                item_id: params.item_id || '',\r\n                item_type: params.item_type || 'question'\r\n            };\r\n            \r\n            // Use AjaxService post method which handles FormData automatically\r\n            return await this.ajaxService.post('grade_pronunciation', data);\r\n            \r\n        } catch (error) {\r\n            console.error('Error grading recording:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get pronunciation topics\r\n     * @returns {Promise<Array>} - List of pronunciation topics\r\n     */\r\n    async getTopics() {\r\n        try {\r\n            return await this.ajaxService.post('get_pronunciation_topics');\r\n        } catch (error) {\r\n            console.error('Error fetching topics:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get topic items (questions and vocabulary)\r\n     * @param {number} topicId - Topic ID\r\n     * @returns {Promise<Array>} - List of topic items\r\n     */\r\n    async getTopicItems(topicId) {\r\n        try {\r\n            return await this.ajaxService.post('get_topic_items', {\r\n                topic_id: topicId\r\n            });\r\n        } catch (error) {\r\n            console.error('Error fetching topic items:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Save pronunciation practice result\r\n     * @param {Object} resultData - Practice result data\r\n     * @returns {Promise<Object>} - Save result\r\n     */\r\n    async saveResult(resultData) {\r\n        try {\r\n            return await this.ajaxService.post('save_pronunciation_result', {\r\n                result_data: JSON.stringify(resultData)\r\n            });\r\n        } catch (error) {\r\n            console.error('Error saving result:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get user's pronunciation history\r\n     * @param {Object} filters - Filter options\r\n     * @returns {Promise<Array>} - List of practice results\r\n     */\r\n    async getHistory(filters = {}) {\r\n        try {\r\n            const data = {};\r\n            \r\n            // Add filters if provided\r\n            if (filters.topic_id) {\r\n                data.topic_id = filters.topic_id;\r\n            }\r\n            if (filters.limit) {\r\n                data.limit = filters.limit;\r\n            }\r\n            if (filters.offset) {\r\n                data.offset = filters.offset;\r\n            }\r\n            \r\n            return await this.ajaxService.post('get_pronunciation_history', data);\r\n        } catch (error) {\r\n            console.error('Error fetching history:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get suggestions for pronunciation practice\r\n     * @returns {Promise<Array>} - List of suggestions\r\n     */\r\n    async getSuggestions({prompt, subject}) {\r\n        try {\r\n            let data = {\r\n                \"prompt\": prompt,\r\n                \"subject\": subject\r\n            }\r\n            return await this.ajaxService.post('get_pronunciation_suggestions', data);\r\n        } catch (error) {\r\n            console.error('Error fetching suggestions:', error);\r\n            throw error;\r\n        }\r\n    }\r\n}\r\n\r\n// Export for use in other modules\r\nexport default PronunciationService;\r\n", "/**\r\n * TOEIC Practice Plugin - REST Service\r\n * \r\n * A service class that provides a secure interface for making REST API requests\r\n * to WordPress backend, handling nonces and other security aspects.\r\n */\r\nclass RestService {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.restUrl - WordPress REST API URL (default: from global toeicPracticeRest)\r\n     * @param {string} options.nonce - Security nonce (default: from global toeicPracticeRest)\r\n     * @param {Function} options.onError - Global error handler (optional)\r\n     */\r\n    constructor(options = {}) {\r\n        // Check if global toeicPracticeRest is available\r\n        const globalData = window.toeicPracticeRest || {};\r\n        \r\n        this.restUrl = options.restUrl || globalData.restUrl || '/wp-json/toeic-practice/v1';\r\n        this.nonce = options.nonce || globalData.restNonce || '';\r\n        this.onError = options.onError || null;\r\n        \r\n        // Validate required parameters\r\n        if (!this.restUrl) {\r\n            console.error('RestService: restUrl is required');\r\n        }\r\n        \r\n        if (!this.nonce) {\r\n            console.warn('RestService: nonce is not provided, requests may fail security checks');\r\n        }\r\n        \r\n        // Keep track of pending requests\r\n        this.pendingRequests = new Map();\r\n    }\r\n    \r\n    /**\r\n     * Make a GET request\r\n     * \r\n     * @param {string} endpoint - REST API endpoint\r\n     * @param {Object} params - Query parameters (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    get(endpoint, params = {}, options = {}) {\r\n        // Build query string\r\n        const queryString = Object.keys(params).length \r\n            ? '?' + new URLSearchParams(params).toString() \r\n            : '';\r\n        \r\n        const url = `${this.restUrl}${endpoint}${queryString}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'GET',\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request\r\n     * \r\n     * @param {string} endpoint - REST API endpoint\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    post(endpoint, data = {}, options = {}) {\r\n        const url = `${this.restUrl}${endpoint}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify(data),\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a request with the fetch API\r\n     * \r\n     * @param {string} url - Request URL\r\n     * @param {Object} options - Request options\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     * @private\r\n     */\r\n    _makeRequest(url, options) {\r\n        // Generate a unique request ID\r\n        const requestId = Math.random().toString(36).substring(2, 15);\r\n        \r\n        // Create abort controller for timeout\r\n        const controller = new AbortController();\r\n        const { signal } = controller;\r\n        \r\n        // Set timeout if specified\r\n        const timeout = options.timeout || 30000; // Default 30 seconds\r\n        const timeoutId = setTimeout(() => {\r\n            controller.abort();\r\n        }, timeout);\r\n        \r\n        // Store the request in pending requests\r\n        this.pendingRequests.set(requestId, { controller });\r\n        \r\n        // Show loader if specified\r\n        const loader = options.loader || (window.toeicLoader || null);\r\n        const showLoader = options.showLoader !== false;\r\n        \r\n        if (loader && showLoader) {\r\n            loader.show(options.loaderText || 'Loading...');\r\n        }\r\n        \r\n        // Add nonce to headers\r\n        const headers = options.headers || {};\r\n        headers['X-WP-Nonce'] = this.nonce; // Use the REST API nonce\r\n\r\n        // Make the request\r\n        return fetch(url, {\r\n            ...options,\r\n            headers,\r\n            signal,\r\n            credentials: 'same-origin'\r\n        })\r\n        .then(response => {\r\n            // Clear timeout\r\n            clearTimeout(timeoutId);\r\n\r\n            // Check if response is OK\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);\r\n            }\r\n            \r\n            // Parse response based on content type\r\n            const contentType = response.headers.get('content-type');\r\n            if (contentType && contentType.includes('application/json')) {\r\n                return response.json();\r\n            }\r\n            \r\n            return response.text();\r\n        })\r\n        .catch(error => {\r\n            // Handle aborted requests\r\n            if (error.name === 'AbortError') {\r\n                throw new Error('Request timed out');\r\n            }\r\n            \r\n            // Call global error handler if available\r\n            if (this.onError) {\r\n                this.onError(error);\r\n            }\r\n            \r\n            throw error;\r\n        })\r\n        .finally(() => {\r\n            // Remove from pending requests\r\n            this.pendingRequests.delete(requestId);\r\n            \r\n            // Hide loader\r\n            if (loader && showLoader) {\r\n                // Only hide if no other requests are pending with this loader\r\n                const otherRequestsWithLoader = Array.from(this.pendingRequests.values())\r\n                    .some(req => req.loader === loader);\r\n                \r\n                if (!otherRequestsWithLoader) {\r\n                    loader.hide();\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Cancel all pending requests\r\n     */\r\n    cancelAll() {\r\n        this.pendingRequests.forEach(request => {\r\n            request.controller.abort();\r\n        });\r\n        \r\n        this.pendingRequests.clear();\r\n    }\r\n    \r\n    /**\r\n     * Update the nonce\r\n     * \r\n     * @param {string} nonce - New nonce value\r\n     */\r\n    updateNonce(nonce) {\r\n        this.nonce = nonce;\r\n    }\r\n    \r\n    /**\r\n     * Create a new instance with custom settings\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @returns {RestService} - New RestService instance\r\n     */\r\n    static create(options = {}) {\r\n        return new RestService(options);\r\n    }\r\n}\r\n\r\n// Create and export default instance\r\nconst restService = new RestService();\r\n\r\nexport default restService;\r\n\r\n// Also export the class for creating custom instances\r\nexport { RestService };\r\n", "/**\r\n * Test Service\r\n * \r\n * Handles all test-related REST API operations.\r\n * Uses the RestService for making requests to the server.\r\n */\r\n\r\nimport restService from './RestService';\r\n\r\nclass TestService {\r\n    /**\r\n     * Fetch test details by ID\r\n     * \r\n     * @param {number} testId - The ID of the test to fetch\r\n     * @returns {Promise} - Promise that resolves with test details\r\n     */\r\n    getTestDetails(testId) {\r\n        return restService.get(`/tests/${testId}`);\r\n    }\r\n    \r\n    /**\r\n     * Fetch test questions by test ID\r\n     * \r\n     * @param {number} testId - The ID of the test to fetch questions for\r\n     * @returns {Promise} - Promise that resolves with test questions\r\n     */\r\n    getTestQuestions(testId) {\r\n        return restService.get(`/tests/${testId}/questions`);\r\n    }\r\n    \r\n    /**\r\n     * Submit a completed test\r\n     * \r\n     * @param {number} testId - The ID of the test being submitted\r\n     * @param {Object} answers - Object containing question IDs as keys and selected answers as values\r\n     * @param {number} timeTaken - Time taken to complete the test in seconds\r\n     * @returns {Promise} - Promise that resolves with test results\r\n     */\r\n    submitTest(testId, answers, timeTaken) {\r\n        return restService.post(`/tests/${testId}/submit`, {\r\n            answers: answers,\r\n            time_taken: timeTaken\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Get all available tests\r\n     * \r\n     * @param {Object} filters - Optional filters for tests (e.g., category, difficulty)\r\n     * @returns {Promise} - Promise that resolves with list of available tests\r\n     */\r\n    getAvailableTests(filters = {}) {\r\n        return restService.get('/tests', filters);\r\n    }\r\n    \r\n    /**\r\n     * Get test results history for current user\r\n     * \r\n     * @param {number} limit - Optional limit for number of results to return\r\n     * @param {number} page - Optional page number for pagination\r\n     * @returns {Promise} - Promise that resolves with test results history\r\n     */\r\n    getTestResultsHistory(limit = 10, page = 1) {\r\n        return restService.get('/results', {\r\n            limit: limit,\r\n            page: page\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Get detailed test result by result ID\r\n     * \r\n     * @param {number} resultId - The ID of the test result to fetch\r\n     * @returns {Promise} - Promise that resolves with detailed test result\r\n     */\r\n    getTestResult(resultId) {\r\n        return restService.get(`/results/${resultId}`);\r\n    }\r\n    \r\n    /**\r\n     * Save user progress on a test (for resuming later)\r\n     * \r\n     * @param {number} testId - The ID of the test\r\n     * @param {Object} progress - Progress data including answers and current position\r\n     * @returns {Promise} - Promise that resolves with save status\r\n     */\r\n    saveTestProgress(testId, progress) {\r\n        return restService.post(`/tests/${testId}/progress`, {\r\n            progress: progress,\r\n            current_question: progress.currentQuestion || 0\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Load saved test progress\r\n     * \r\n     * @param {number} testId - The ID of the test to load progress for\r\n     * @returns {Promise} - Promise that resolves with saved progress data\r\n     */\r\n    loadTestProgress(testId) {\r\n        return restService.get(`/tests/${testId}/progress`);\r\n    }\r\n    \r\n    /**\r\n     * Get test statistics for a specific test\r\n     * \r\n     * @param {number} testId - The ID of the test\r\n     * @returns {Promise} - Promise that resolves with test statistics\r\n     */\r\n    getTestStatistics(testId) {\r\n        return restService.get(`/tests/${testId}/statistics`);\r\n    }\r\n    \r\n    /**\r\n     * Get section details by section ID\r\n     * \r\n     * @param {number} sectionId - The ID of the section to fetch\r\n     * @returns {Promise} - Promise that resolves with section details\r\n     */\r\n    getSectionDetails(sectionId) {\r\n        // This method can be implemented when needed\r\n        return Promise.resolve({});\r\n    }\r\n}\r\n\r\n// Create and export a singleton instance\r\nconst testService = new TestService();\r\nexport default testService;\r\n\r\n// Also export the class for potential extension\r\nexport { TestService };\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\r\n * TOEIC Practice Plugin - Frontend Main Entry Point\r\n * \r\n * This file serves as the main entry point for the frontend JavaScript\r\n * functionality of the TOEIC Practice plugin.\r\n */\r\n\r\n// Import components\r\nimport Sidebar from './components/Sidebar';\r\nimport Loader from './components/Loader';\r\nimport ajaxService, { AjaxService } from './services/AjaxService';\r\nimport testService, { TestService } from './services/TestService';\r\nimport TestDetailPage from './pages/test-detail-page';\r\nimport { PronunciationRecorder } from './pages/pronunciation-page';\r\n\r\n// Initialize the application when DOM is fully loaded\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n    // Global loader instance for full-screen loading\r\n    window.toeicLoader = new Loader({\r\n        fullScreen: true\r\n    });\r\n    \r\n    // Make services available globally\r\n    window.toeicAjax = ajaxService;\r\n    window.toeicTest = testService;\r\n\r\n    // check if record button exists\r\n    const recordBtn = document.getElementById('record-btn');\r\n    if (recordBtn) {\r\n        window.toeicPronunciation = new PronunciationRecorder({paragraphText: recordBtn.dataset.paragraphText});\r\n    }\r\n});\r\n\r\n// Export components for potential reuse\r\nexport {\r\n    Sidebar,\r\n    Loader,\r\n    ajaxService,\r\n    AjaxService,\r\n    testService,\r\n    TestService,\r\n    TestDetailPage,\r\n};\r\n"], "names": ["Loader", "options", "arguments", "length", "undefined", "_classCallCheck", "containerId", "text", "size", "fullScreen", "container", "loader", "isVisible", "init", "_createClass", "key", "value", "document", "getElementById", "createElement", "id", "className", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "createLoader", "concat", "spinner", "textElement", "textContent", "hide", "show", "updateText", "style", "display", "querySelector", "newTextElement", "toggle", "showFor", "duration", "_this", "Promise", "resolve", "setTimeout", "forElement", "element", "console", "error", "uniqueId", "Math", "random", "toString", "substr", "computedStyle", "window", "getComputedStyle", "position", "_objectSpread", "Sidebar", "menuItems", "querySelectorAll", "setupEventListeners", "highlightCurrentPage", "for<PERSON>ach", "item", "addEventListener", "handleMenuItemClick", "bind", "event", "remove", "currentTarget", "currentPath", "location", "pathname", "link", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "Matching<PERSON><PERSON><PERSON>", "_Question<PERSON><PERSON><PERSON>", "_callSuper", "_inherits", "render", "clearContainer", "items", "question", "JSON", "parse", "e", "matchingC<PERSON>r", "userAnswers", "getUserAnswer", "leftColumn", "rightColumn", "leftHeader", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON>t", "selectedRight", "totalPairs", "pairedCount", "index", "leftItem", "dataset", "itemId", "wordContainer", "wordText", "prompt", "audio_link", "audioButton", "innerHTML", "stopPropagation", "audio", "Audio", "play", "isReviewMode", "toggleLeftSelection", "checkForMatch", "rightItem", "response", "log", "toggleRightSelection", "submitButton", "disabled", "onSubmit", "addStyles", "showCorrectMatches", "showExistingAnswers", "styleEl", "head", "contains", "leftId", "rightId", "answer", "setUserAnswer", "checkAllPaired", "userAnswer", "leftCards", "rightCards", "leftCard", "Array", "from", "find", "card", "rightCard", "wrongRightCard", "shuffleResponses", "responses", "map", "i", "j", "floor", "_ref", "MultipleChoiceR<PERSON>er", "option", "optionElement", "createOptionElement", "_this2", "inputId", "isChecked", "correct_answer", "input", "onAnswerSelected", "constructor", "Error", "isAnswered", "createWrapper", "wrapper", "questionId", "questionType", "type", "TextInputR<PERSON>er", "QuestionRendererFactory", "<PERSON><PERSON><PERSON><PERSON>", "question_type", "toLowerCase", "hasRendererForType", "supportedTypes", "includes", "inputContainer", "placeholder", "reviewInfo", "isCorrect", "target", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "l", "TypeError", "call", "done", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "then", "_asyncToGenerator", "apply", "_next", "_throw", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "_typeof", "toPrimitive", "String", "Number", "PronunciationService", "PronunciationRecorder", "paragraphText", "isRecording", "mediaRecorder", "audioChunks", "startTime", "timerInterval", "pronunciationService", "recordBtn", "recordingStatus", "recordingDuration", "durationText", "suggestionBtns", "warn", "hideRecordingElements", "toggleRecording", "button", "handleSuggestion", "checkMicrophonePermissions", "_checkMicrophonePermissions", "_callee", "stream", "_t", "_context", "navigator", "mediaDevices", "getUserMedia", "getTracks", "track", "stop", "showError", "_toggleRecording", "_callee2", "_context2", "stopRecording", "startRecording", "_startRecording", "_callee3", "mimeType", "_t2", "_context3", "echoCancellation", "noiseSuppression", "sampleRate", "channelCount", "MediaRecorder", "isTypeSupported", "actualMimeType", "ondataavailable", "data", "push", "onstop", "processRecording", "start", "Date", "now", "showRecordingElements", "updateButtonState", "startTimer", "stopTimer", "_this3", "audioBlob", "Blob", "convertToWAV", "wavBlob", "submitForGrading", "_convertToWAV", "_callee5", "_this4", "_context5", "reject", "audioContext", "AudioContext", "webkitAudioContext", "fileReader", "FileReader", "onload", "_ref2", "_callee4", "arrayBuffer", "audioBuffer", "<PERSON><PERSON><PERSON><PERSON>", "_t3", "_context4", "result", "decodeAudioData", "trimSilence", "audioBufferToWav", "_x2", "onerror", "readAsA<PERSON>y<PERSON><PERSON>er", "_x", "silenceT<PERSON><PERSON>old", "marginMs", "numberOfChannels", "marginSamples", "audioData", "getChannelData", "startIndex", "abs", "max", "endIndex", "min", "<PERSON><PERSON><PERSON><PERSON>", "minimalBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "channel", "originalData", "trimmedData", "originalDuration", "toFixed", "trimmedDuration", "trimmedStart", "trimmedEnd", "buffer", "targetSampleRate", "targetChannels", "bitsPerSample", "resampled<PERSON><PERSON><PERSON>", "resampleAndConvertToMono", "bytesPerSample", "blockAlign", "byteRate", "dataSize", "bufferSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "DataView", "writeString", "offset", "string", "setUint8", "charCodeAt", "setUint32", "setUint16", "sample", "intSample", "setInt16", "originalSampleRate", "original<PERSON>ength", "resampleRatio", "resampledData", "Float32Array", "monoData", "sum", "originalIndex", "fraction", "_submitForGrading", "_callee6", "params", "_t4", "_context6", "showLoadingState", "topicItemData", "topic_id", "item_id", "item_type", "gradeRecording", "displayResults", "message", "hideLoadingState", "_x3", "analysis", "scores", "itemData", "item_data", "overallScore", "round", "overall_score", "accuracy", "fluency", "completeness", "pronunciation", "expectedText", "expected_text", "word", "recognizedText", "recognized_text", "feedback", "detectedIssues", "detected_issues", "suggestions", "issuesHtml", "issue", "join", "suggestionsHtml", "suggestion", "practiceResult", "getScoreClass", "score", "recordText", "_this5", "setInterval", "elapsed", "minutes", "seconds", "timeString", "padStart", "clearInterval", "resetRecording", "getNonce", "toeic_ajax_nonce", "subject", "getSuggestion", "_getSuggestion", "_callee7", "_ref3", "_t5", "_context7", "getSuggestions", "_x4", "testService", "TestDetailPage", "testData", "toeicTestData", "questions", "sections", "currentQuestionIndex", "isTestActive", "timer<PERSON><PERSON>", "timerDisplay", "timerMinutes", "timerSeconds", "<PERSON><PERSON><PERSON><PERSON>", "sectionInfo", "sectionTitle", "sectionInstructions", "questionNumber", "questionText", "questionOptions", "prevButton", "nextButton", "questionIndicators", "currentQuestionDisplay", "totalQuestionsDisplay", "resultsContainer", "resultsContent", "reviewButton", "timeLimit", "time_limit", "timeRemaining", "loadQuestions", "getTestQuestions", "renderQuestionIndicators", "startTest", "alert", "goToPreviousQuestion", "goToNextQuestion", "confirmSubmitTest", "startReviewMode", "returnValue", "showCurrentQuestion", "updateTimerDisplay", "elapsedSeconds", "submitTest", "percentRemaining", "width", "backgroundColor", "_", "indicator", "goToQuestion", "updateQuestionIndicators", "indicators", "_this5$questions$inde", "section", "s", "section_id", "title", "instructions", "content", "renderQuestionOptions", "updateNavigationButtons", "renderer", "saveAnswer", "renderFallbackQuestion", "fallbackElement", "isLastQuestion", "answeredCount", "keys", "unansweredCount", "confirm", "_this6", "test_id", "time_taken", "answers", "showResults", "results", "navigationElement", "totalPoints", "total_points", "percentage", "resultClass", "renderSectionScores", "section_scores", "sectionScores", "total", "testDetailElement", "testDetailPage", "AjaxService", "globalData", "toeicPracticeAjax", "ajaxUrl", "nonce", "nonceParam", "onError", "pendingRequests", "Map", "get", "action", "requestParams", "_defineProperty", "queryString", "URLSearchParams", "url", "_makeRequest", "method", "post", "formData", "FormData", "append", "entries", "_slicedToArray", "name", "stringify", "post<PERSON><PERSON>", "requestData", "headers", "requestId", "substring", "controller", "AbortController", "signal", "timeout", "timeoutId", "abort", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loaderText", "fetch", "credentials", "clearTimeout", "ok", "status", "statusText", "contentType", "json", "success", "otherRequestsWithLoader", "values", "some", "req", "cancelAll", "request", "clear", "updateNonce", "ajaxService", "_gradeRecording", "_args", "_getTopics", "getTopics", "_getTopicItems", "topicId", "getTopicItems", "_saveResult", "resultData", "result_data", "saveResult", "_getHistory", "filters", "_args5", "limit", "getHistory", "_getSuggestions", "_t6", "RestService", "toeicPracticeRest", "restUrl", "restNonce", "endpoint", "restService", "TestService", "getTestDetails", "testId", "timeTaken", "getAvailableTests", "getTestResultsHistory", "page", "getTestResult", "resultId", "saveTestProgress", "progress", "current_question", "currentQuestion", "loadTestProgress", "getTestStatistics", "getSectionDetails", "sectionId", "toeicAjax", "toeicTest", "toeicPronunciation"], "sourceRoot": ""}