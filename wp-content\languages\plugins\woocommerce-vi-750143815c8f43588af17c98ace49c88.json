{"translation-revision-date": "2025-06-16 01:31:31+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["Chuyển đổi để tắt mở ngăn Giỏ hàng mini khi nhấp vào biểu tượng giỏ hàng và thay vào đó điều hướng đến trang thanh toán."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["<PERSON><PERSON><PERSON><PERSON> hướng đến thanh toán khi nhấp vào Giỏ hàng mini, thay vì mở ngăn kéo."], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["Trình chỉnh sửa không hiển thị giá trị đếm thực tế, mà là một dấu giữ chỗ để chỉ ra cách nó sẽ trông trên giao diện người dùng."], "Only if cart has items": ["Chỉ khi giỏ hàng có sản phẩm"], "Always (even if empty)": ["<PERSON><PERSON><PERSON> luôn (kể cả khi trống)"], "Show Cart Item Count:": ["Hiển thị số lượng sản phẩm trong giỏ hàng:"], "Product Count": ["S<PERSON> l<PERSON> sản phẩm"], "Cart Icon": ["<PERSON><PERSON><PERSON><PERSON> tượng Giỏ hàng"], "Icon": ["<PERSON><PERSON><PERSON><PERSON>"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Chuyển đổi để mở ngăn Giỏ hàng mini khi người mua hàng thêm sản phẩm vào giỏ hàng của họ."], "Open drawer when adding": ["Mở ngăn khi thêm"], "Behavior": ["Hành vi"], "Edit Mini-Cart Drawer template": ["Chỉnh sửa mẫu Ngăn kéo Giỏ hàng Mini"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["<PERSON><PERSON> được mở, ng<PERSON>n Giỏ hàng Mini cho phép người mua hàng truy cập nhanh để xem các sản phẩm họ đã chọn và thanh toán."], "Cart Drawer": ["<PERSON><PERSON><PERSON> kéo giỏ hàng"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Chuyển đổi để hiển thị tổng giá của các sản phẩm trong giỏ hàng. <PERSON><PERSON><PERSON> không có sản phẩm nào được thêm vào, gi<PERSON> sẽ không hiển thị."], "Display total price": ["<PERSON><PERSON><PERSON> thị tổng giá"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["<PERSON>ọn cách Giỏ hàng mini hoạt động trong trang Giỏ hàng và Than<PERSON> toán. Đi<PERSON>u này có thể ảnh hưởng đến bố cục tiêu đề."], "Mini-Cart in cart and checkout pages": ["Giỏ hàng mini trong trang giỏ hàng và thanh toán"], "Hide": ["<PERSON><PERSON><PERSON><PERSON>"], "Display": ["<PERSON><PERSON><PERSON> thị"], "Never": ["<PERSON><PERSON><PERSON><PERSON> thời hạn"], "Price": ["Giá"], "Remove": ["Xoá"], "Settings": ["Cài đặt"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}