<?php
/**
 * Plugin Name: Toeic Practice
 * Plugin URI: https://example.com/toeic-practice
 * Description: A comprehensive TOEIC practice plugin for WordPress with listening and reading exercises.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: toeic-practice
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package ToeicPractice
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('TOEIC_PRACTICE_VERSION', '1.0.0');
define('TOEIC_PRACTICE_PLUGIN_FILE', __FILE__);
define('TOEIC_PRACTICE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('TOEIC_PRACTICE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TOEIC_PRACTICE_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Require the autoloader
require_once TOEIC_PRACTICE_PLUGIN_DIR . 'vendor/autoload.php';

// Autoloader
spl_autoload_register(function ($class) {
    $prefix = 'ToeicPractice\\';
    $base_dir = __DIR__ . '/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) {
        require $file;
    }
});

// Initialize the plugin
add_action('plugins_loaded', function() {
    \ToeicPractice\Plugin::getInstance();
});

// Activation hook
register_activation_hook(__FILE__, function() {
    \ToeicPractice\Activator::activate();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    \ToeicPractice\Deactivator::deactivate();
});

// Uninstall hook
register_uninstall_hook(__FILE__, ['\ToeicPractice\Uninstaller', 'uninstall']);
