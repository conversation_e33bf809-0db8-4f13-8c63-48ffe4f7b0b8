<?php
/**
 * Admin Manager
 *
 * @package ToeicPractice\Admin
 */

namespace ToeicPractice\Admin;

use ToeicPractice\Admin\Pages\TestPage;
use ToeicPractice\Admin\Pages\TestEditPage;
use ToeicPractice\Admin\Pages\QuestionPage;
use ToeicPractice\Admin\Pages\VocabularyPage;
use ToeicPractice\Admin\Pages\PronunciationTopicPage;
use ToeicPractice\Admin\Pages\PronunciationTopicEditPage;
use ToeicPractice\Admin\Pages\AiSuggestionPage;

/**
 * Admin Manager Class
 * 
 * Handles all admin-related functionality including menu pages,
 * settings, and admin-only features.
 */
class AdminManager {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init();
    }
    
    /**
     * Initialize admin functionality
     */
    private function init() {
        // Add admin menu
        add_action('admin_menu', [$this, 'addAdminMenu']);
        
        // Add admin notices
        add_action('admin_notices', [$this, 'showAdminNotices']);
        
        // Register settings
        add_action('admin_init', [$this, 'registerSettings']);
        
        // Add meta boxes
        add_action('add_meta_boxes', [$this, 'addMetaBoxes']);
        
        // Save meta box data
        add_action('save_post', [$this, 'saveMetaBoxData']);
        
        // Add admin columns
        add_filter('manage_toeic_test_posts_columns', [$this, 'addTestColumns']);
        add_action('manage_toeic_test_posts_custom_column', [$this, 'displayTestColumns'], 10, 2);
        
        add_filter('manage_toeic_question_posts_columns', [$this, 'addQuestionColumns']);
        add_action('manage_toeic_question_posts_custom_column', [$this, 'displayQuestionColumns'], 10, 2);
        
        // Add bulk actions
        add_filter('bulk_actions-edit-toeic_test', [$this, 'addBulkActions']);
        add_filter('handle_bulk_actions-edit-toeic_test', [$this, 'handleBulkActions'], 10, 3);
        
        // Add admin bar menu
        add_action('admin_bar_menu', [$this, 'addAdminBarMenu'], 100);
    }
    
    /**
     * Add admin menu pages
     */
    public function addAdminMenu() {
        // Main menu page
        add_menu_page(
            __('TOEIC Practice', 'toeic-practice'),
            __('TOEIC Practice', 'toeic-practice'),
            'manage_options',
            'toeic-practice',
            [$this, 'displayDashboardPage'],
            'dashicons-welcome-learn-more',
            30
        );
        
        // Dashboard submenu (same as main page)
        add_submenu_page(
            'toeic-practice',
            __('Dashboard', 'toeic-practice'),
            __('Dashboard', 'toeic-practice'),
            'manage_options',
            'toeic-practice',
            [$this, 'displayDashboardPage']
        );
        
        // Tests submenu
        add_submenu_page(
            'toeic-practice',
            __('Tests', 'toeic-practice'),
            __('Tests', 'toeic-practice'),
            'manage_options',
            'toeic-practice-tests',
            [$this, 'displayTestPage']
        );
        
        // Questions submenu
        add_submenu_page(
            'toeic-practice',
            __('Questions', 'toeic-practice'),
            __('Questions', 'toeic-practice'),
            'manage_options',
            'toeic-practice-questions',
            [$this, 'displayQuestionsPage']
        );
        
        // Vocabulary submenu
        add_submenu_page(
            'toeic-practice',
            __('Vocabulary', 'toeic-practice'),
            __('Vocabulary', 'toeic-practice'),
            'manage_options',
            'toeic-practice-vocabulary',
            [$this, 'displayVocabularyPage']
        );
        
        // Pronunciation Topics submenu
        add_submenu_page(
            'toeic-practice',
            __('Pronunciation Topics', 'toeic-practice'),
            __('Pronunciation Topics', 'toeic-practice'),
            'manage_options',
            'toeic-practice-pronunciation-topics',
            [$this, 'displayPronunciationTopicsPage']
        );

        // AI Suggestions submenu
        add_submenu_page(
            'toeic-practice',
            __('AI Suggestions', 'toeic-practice'),
            __('AI Suggestions', 'toeic-practice'),
            'manage_options',
            'toeic-practice-ai-suggestions',
            [$this, 'displayAiSuggestionsPage']
        );

        // Results submenu
        add_submenu_page(
            'toeic-practice',
            __('Results', 'toeic-practice'),
            __('Results', 'toeic-practice'),
            'manage_options',
            'toeic-practice-results',
            [$this, 'displayResultsPage']
        );

        // Settings submenu
        add_submenu_page(
            'toeic-practice',
            __('Settings', 'toeic-practice'),
            __('Settings', 'toeic-practice'),
            'manage_options',
            'toeic-practice-settings',
            [$this, 'displaySettingsPage']
        );
    }
    
    /**
     * Display dashboard page
     */
    public function displayDashboardPage() {
        $dashboard = new Pages\DashboardPage();
        $dashboard->display();
    }
    
    /**
     * Display tests page
     */
    public function displayTestPage() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        
        switch ($action) {
            case 'add':
            case 'edit':
                $page = new TestEditPage();
                $page->display();
                break;
                
            default:
                $page = new TestPage();
                $page->display();
                break;
        }
    }
    
    /**
     * Display questions page
     */
    public function displayQuestionsPage() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        
        switch ($action) {
            case 'add':
                $page = new Pages\QuestionAddPage();
                $page->display();
                break;
                
            case 'edit':
                $page = new Pages\QuestionEditPage();
                $page->display();
                break;
                
            default:
                $page = new QuestionPage();
                $page->display();
                break;
        }
    }
    
    /**
     * Display vocabulary page
     */
    public function displayVocabularyPage() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        
        switch ($action) {
            case 'add':
                $page = new Pages\VocabularyAddPage();
                $page->display();
                break;
                
            case 'edit':
                $page = new Pages\VocabularyEditPage();
                $page->display();
                break;
                
            default:
                $page = new VocabularyPage();
                $page->display();
                break;
        }
    }
    
    /**
     * Display pronunciation topics page
     */
    public function displayPronunciationTopicsPage() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';

        switch ($action) {
            case 'add':
            case 'edit':
                $page = new PronunciationTopicEditPage();
                $page->display();
                break;

            default:
                $page = new PronunciationTopicPage();
                $page->display();
                break;
        }
    }

    /**
     * Display AI suggestions page
     */
    public function displayAiSuggestionsPage() {
        $ai_suggestions = new Pages\AiSuggestionPage();
        $ai_suggestions->display();
    }

    /**
     * Display settings page
     */
    public function displaySettingsPage() {
        $settings = new Pages\SettingsPage();
        $settings->display();
    }
    
    /**
     * Show admin notices
     */
    public function showAdminNotices() {
        // Check for plugin activation notice
        if (get_transient('toeic_practice_activation_notice')) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . __('TOEIC Practice plugin has been activated successfully!', 'toeic-practice') . '</p>';
            echo '</div>';
            delete_transient('toeic_practice_activation_notice');
        }
        
        // Check for settings saved notice
        if (isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true') {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . __('Settings saved successfully!', 'toeic-practice') . '</p>';
            echo '</div>';
        }
        
        // Check for errors
        $error_message = get_transient('toeic_practice_error_notice');
        if ($error_message) {
            echo '<div class="notice notice-error is-dismissible">';
            echo '<p>' . esc_html($error_message) . '</p>';
            echo '</div>';
            delete_transient('toeic_practice_error_notice');
        }
    }
    
    /**
     * Register plugin settings
     */
    public function registerSettings() {
        // General settings
        register_setting('toeic_practice_general', 'toeic_practice_enable_listening');
        register_setting('toeic_practice_general', 'toeic_practice_enable_reading');
        register_setting('toeic_practice_general', 'toeic_practice_time_limit');
        register_setting('toeic_practice_general', 'toeic_practice_questions_per_test');
        register_setting('toeic_practice_general', 'toeic_practice_show_explanations');
        register_setting('toeic_practice_general', 'toeic_practice_allow_retakes');
        register_setting('toeic_practice_general', 'toeic_practice_save_progress');
        
        // Display settings
        register_setting('toeic_practice_display', 'toeic_practice_theme_color');
        register_setting('toeic_practice_display', 'toeic_practice_custom_css');
        register_setting('toeic_practice_display', 'toeic_practice_show_timer');
        register_setting('toeic_practice_display', 'toeic_practice_show_progress_bar');
        
        // Email settings
        register_setting('toeic_practice_email', 'toeic_practice_email_notifications');
        register_setting('toeic_practice_email', 'toeic_practice_admin_email');
        register_setting('toeic_practice_email', 'toeic_practice_email_template');
        
        // Advanced settings
        register_setting('toeic_practice_advanced', 'toeic_practice_data_retention');
        register_setting('toeic_practice_advanced', 'toeic_practice_debug_mode');
        
        // Azure settings
        register_setting('toeic_practice_azure', 'toeic_practice_azure_stt_subscription_key');
        register_setting('toeic_practice_azure', 'toeic_practice_azure_stt_service_region');
        register_setting('toeic_practice_azure', 'toeic_practice_azure_translator_subscription_key');
        register_setting('toeic_practice_azure', 'toeic_practice_azure_translator_service_region');
        
        // AI Suggestion settings
        register_setting('toeic_practice_ai_suggestion', 'toeic_practice_ai_suggestion_list');
    }
    
    /**
     * Add meta boxes
     */
    public function addMetaBoxes() {
        // Test meta boxes
        add_meta_box(
            'toeic_test_details',
            __('Test Details', 'toeic-practice'),
            [$this, 'displayTestDetailsMetaBox'],
            'toeic_test',
            'normal',
            'high'
        );
        
        add_meta_box(
            'toeic_test_questions',
            __('Test Questions', 'toeic-practice'),
            [$this, 'displayTestQuestionsMetaBox'],
            'toeic_test',
            'normal',
            'high'
        );
        
        // Question meta boxes
        add_meta_box(
            'toeic_question_details',
            __('Question Details', 'toeic-practice'),
            [$this, 'displayQuestionDetailsMetaBox'],
            'toeic_question',
            'normal',
            'high'
        );
        
        add_meta_box(
            'toeic_question_answers',
            __('Answer Options', 'toeic-practice'),
            [$this, 'displayQuestionAnswersMetaBox'],
            'toeic_question',
            'normal',
            'high'
        );
    }
    
    /**
     * Save meta box data
     */
    public function saveMetaBoxData($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check nonce
        if (!isset($_POST['toeic_practice_nonce']) || !wp_verify_nonce($_POST['toeic_practice_nonce'], 'toeic_practice_meta_box')) {
            return;
        }
        
        $post_type = get_post_type($post_id);
        
        if ($post_type === 'toeic_test') {
            $this->saveTestMetaData($post_id);
        } elseif ($post_type === 'toeic_question') {
            $this->saveQuestionMetaData($post_id);
        }
    }
    
    /**
     * Save test meta data
     */
    private function saveTestMetaData($post_id) {
        $fields = [
            'test_type',
            'difficulty_level',
            'time_limit',
            'total_questions',
            'passing_score',
            'instructions'
        ];
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
        
        // Save selected questions
        if (isset($_POST['selected_questions'])) {
            $questions = array_map('intval', $_POST['selected_questions']);
            update_post_meta($post_id, '_selected_questions', $questions);
        }
    }
    
    /**
     * Save question meta data
     */
    private function saveQuestionMetaData($post_id) {
        $fields = [
            'question_type',
            'difficulty_level',
            'section',
            'audio_file',
            'image_file',
            'explanation',
            'correct_answer'
        ];
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
        
        // Save answer options
        if (isset($_POST['answer_options'])) {
            $options = array_map('sanitize_text_field', $_POST['answer_options']);
            update_post_meta($post_id, '_answer_options', $options);
        }
    }
    
    /**
     * Add custom columns for tests
     */
    public function addTestColumns($columns) {
        $new_columns = [];
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['test_type'] = __('Type', 'toeic-practice');
        $new_columns['difficulty'] = __('Difficulty', 'toeic-practice');
        $new_columns['questions'] = __('Questions', 'toeic-practice');
        $new_columns['date'] = $columns['date'];
        
        return $new_columns;
    }
    
    /**
     * Display custom columns for tests
     */
    public function displayTestColumns($column, $post_id) {
        switch ($column) {
            case 'test_type':
                echo esc_html(get_post_meta($post_id, '_test_type', true));
                break;
            case 'difficulty':
                echo esc_html(get_post_meta($post_id, '_difficulty_level', true));
                break;
            case 'questions':
                $questions = get_post_meta($post_id, '_selected_questions', true);
                echo is_array($questions) ? count($questions) : '0';
                break;
        }
    }
    
    /**
     * Add custom columns for questions
     */
    public function addQuestionColumns($columns) {
        $new_columns = [];
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['question_type'] = __('Type', 'toeic-practice');
        $new_columns['section'] = __('Section', 'toeic-practice');
        $new_columns['difficulty'] = __('Difficulty', 'toeic-practice');
        $new_columns['date'] = $columns['date'];
        
        return $new_columns;
    }
    
    /**
     * Display custom columns for questions
     */
    public function displayQuestionColumns($column, $post_id) {
        switch ($column) {
            case 'question_type':
                echo esc_html(get_post_meta($post_id, '_question_type', true));
                break;
            case 'section':
                echo esc_html(get_post_meta($post_id, '_section', true));
                break;
            case 'difficulty':
                echo esc_html(get_post_meta($post_id, '_difficulty_level', true));
                break;
        }
    }
    
    /**
     * Add bulk actions
     */
    public function addBulkActions($actions) {
        $actions['duplicate_test'] = __('Duplicate', 'toeic-practice');
        $actions['export_test'] = __('Export', 'toeic-practice');
        return $actions;
    }
    
    /**
     * Handle bulk actions
     */
    public function handleBulkActions($redirect_to, $action, $post_ids) {
        if ($action === 'duplicate_test') {
            foreach ($post_ids as $post_id) {
                $this->duplicateTest($post_id);
            }
            $redirect_to = add_query_arg('duplicated', count($post_ids), $redirect_to);
        } elseif ($action === 'export_test') {
            $this->exportTests($post_ids);
        }
        
        return $redirect_to;
    }
    
    /**
     * Duplicate a test
     */
    private function duplicateTest($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        $new_post = [
            'post_title' => $post->post_title . ' (Copy)',
            'post_content' => $post->post_content,
            'post_status' => 'draft',
            'post_type' => $post->post_type,
            'post_author' => get_current_user_id()
        ];
        
        $new_post_id = wp_insert_post($new_post);
        
        if ($new_post_id) {
            // Copy meta data
            $meta_data = get_post_meta($post_id);
            foreach ($meta_data as $key => $values) {
                foreach ($values as $value) {
                    update_post_meta($new_post_id, $key, maybe_unserialize($value));
                }
            }
        }
        
        return $new_post_id;
    }
    
    /**
     * Export tests
     */
    private function exportTests($post_ids) {
        // Implementation for exporting tests
        // This would generate a downloadable file with test data
    }
    
    /**
     * Add admin bar menu
     */
    public function addAdminBarMenu($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $wp_admin_bar->add_menu([
            'id' => 'toeic-practice',
            'title' => __('TOEIC Practice', 'toeic-practice'),
            'href' => admin_url('admin.php?page=toeic-practice')
        ]);
        
        $wp_admin_bar->add_menu([
            'parent' => 'toeic-practice',
            'id' => 'toeic-practice-dashboard',
            'title' => __('Dashboard', 'toeic-practice'),
            'href' => admin_url('admin.php?page=toeic-practice')
        ]);
        
        $wp_admin_bar->add_menu([
            'parent' => 'toeic-practice',
            'id' => 'toeic-practice-new-test',
            'title' => __('New Test', 'toeic-practice'),
            'href' => admin_url('post-new.php?post_type=toeic_test')
        ]);
        
        $wp_admin_bar->add_menu([
            'parent' => 'toeic-practice',
            'id' => 'toeic-practice-new-question',
            'title' => __('New Question', 'toeic-practice'),
            'href' => admin_url('post-new.php?post_type=toeic_question')
        ]);
    }
}
