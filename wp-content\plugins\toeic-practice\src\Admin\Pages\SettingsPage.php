<?php
/**
 * Settings Page
 *
 * @package ToeicPractice\Admin\Pages
 */

namespace ToeicPractice\Admin\Pages;

/**
 * Settings Page Class
 * 
 * Handles the display and functionality of the plugin settings page.
 */
class SettingsPage {
    
    /**
     * Display the settings page
     */
    public function display() {
        $tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
        
        ?>
        <div class="wrap toeic-practice-settings">
            <h1><?php echo esc_html__('TOEIC Practice Settings', 'toeic-practice'); ?></h1>
            
            <h2 class="nav-tab-wrapper">
                <a href="?page=toeic-practice-settings&tab=general" class="nav-tab <?php echo $tab === 'general' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('General', 'toeic-practice'); ?>
                </a>
                <a href="?page=toeic-practice-settings&tab=display" class="nav-tab <?php echo $tab === 'display' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Display', 'toeic-practice'); ?>
                </a>
                <a href="?page=toeic-practice-settings&tab=email" class="nav-tab <?php echo $tab === 'email' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Email Notifications', 'toeic-practice'); ?>
                </a>
                <a href="?page=toeic-practice-settings&tab=advanced" class="nav-tab <?php echo $tab === 'advanced' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Advanced', 'toeic-practice'); ?>
                </a>
                <a href="?page=toeic-practice-settings&tab=azure" class="nav-tab <?php echo $tab === 'azure' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Azure Pronunciation', 'toeic-practice'); ?>
                </a>
            </h2>
            
            <form method="post" action="options.php">
                <?php
                switch ($tab) {
                    case 'display':
                        settings_fields('toeic_practice_display');
                        $this->displayDisplaySettings();
                        break;
                    case 'email':
                        settings_fields('toeic_practice_email');
                        $this->displayEmailSettings();
                        break;
                    case 'advanced':
                        settings_fields('toeic_practice_advanced');
                        $this->displayAdvancedSettings();
                        break;
                    case 'azure':
                        settings_fields('toeic_practice_azure');
                        $this->displayAzureSettings();
                        break;
                    default:
                        settings_fields('toeic_practice_general');
                        $this->displayGeneralSettings();
                        break;
                }
                ?>
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo esc_attr__('Save Changes', 'toeic-practice'); ?>">
                </p>
            </form>
        </div>
        <?php
    }
    
    /**
     * Display general settings
     */
    private function displayGeneralSettings() {
        ?>
        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_enable_listening"><?php echo esc_html__('Enable Listening Section', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_enable_listening" id="toeic_practice_enable_listening" value="1" <?php checked(get_option('toeic_practice_enable_listening', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Enable the listening section in TOEIC tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_enable_reading"><?php echo esc_html__('Enable Reading Section', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_enable_reading" id="toeic_practice_enable_reading" value="1" <?php checked(get_option('toeic_practice_enable_reading', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Enable the reading section in TOEIC tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_time_limit"><?php echo esc_html__('Default Time Limit (minutes)', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="number" name="toeic_practice_time_limit" id="toeic_practice_time_limit" value="<?php echo esc_attr(get_option('toeic_practice_time_limit', 120)); ?>" class="regular-text" min="1" max="999">
                        <p class="description"><?php echo esc_html__('Default time limit for tests in minutes.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_questions_per_test"><?php echo esc_html__('Default Questions Per Test', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="number" name="toeic_practice_questions_per_test" id="toeic_practice_questions_per_test" value="<?php echo esc_attr(get_option('toeic_practice_questions_per_test', 100)); ?>" class="regular-text" min="1" max="999">
                        <p class="description"><?php echo esc_html__('Default number of questions per test.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_show_explanations"><?php echo esc_html__('Show Explanations', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_show_explanations" id="toeic_practice_show_explanations" value="1" <?php checked(get_option('toeic_practice_show_explanations', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Show explanations for correct answers after test completion.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_allow_retakes"><?php echo esc_html__('Allow Test Retakes', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_allow_retakes" id="toeic_practice_allow_retakes" value="1" <?php checked(get_option('toeic_practice_allow_retakes', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Allow users to retake tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_save_progress"><?php echo esc_html__('Save Test Progress', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_save_progress" id="toeic_practice_save_progress" value="1" <?php checked(get_option('toeic_practice_save_progress', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Save user progress during tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Display display settings
     */
    private function displayDisplaySettings() {
        ?>
        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_theme_color"><?php echo esc_html__('Theme Color', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="color" name="toeic_practice_theme_color" id="toeic_practice_theme_color" value="<?php echo esc_attr(get_option('toeic_practice_theme_color', '#0073aa')); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Main theme color for the plugin.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_custom_css"><?php echo esc_html__('Custom CSS', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <textarea name="toeic_practice_custom_css" id="toeic_practice_custom_css" rows="10" class="large-text code"><?php echo esc_textarea(get_option('toeic_practice_custom_css', '')); ?></textarea>
                        <p class="description"><?php echo esc_html__('Add custom CSS to customize the appearance.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_show_timer"><?php echo esc_html__('Show Timer', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_show_timer" id="toeic_practice_show_timer" value="1" <?php checked(get_option('toeic_practice_show_timer', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Show countdown timer during tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_show_progress_bar"><?php echo esc_html__('Show Progress Bar', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_show_progress_bar" id="toeic_practice_show_progress_bar" value="1" <?php checked(get_option('toeic_practice_show_progress_bar', 1)); ?>>
                        <p class="description"><?php echo esc_html__('Show progress bar during tests.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Display email settings
     */
    private function displayEmailSettings() {
        ?>
        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_email_notifications"><?php echo esc_html__('Enable Email Notifications', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_email_notifications" id="toeic_practice_email_notifications" value="1" <?php checked(get_option('toeic_practice_email_notifications', 0)); ?>>
                        <p class="description"><?php echo esc_html__('Send email notifications for test completions.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_admin_email"><?php echo esc_html__('Admin Email', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="email" name="toeic_practice_admin_email" id="toeic_practice_admin_email" value="<?php echo esc_attr(get_option('toeic_practice_admin_email', get_option('admin_email'))); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Email address for admin notifications.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_email_template"><?php echo esc_html__('Email Template', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <textarea name="toeic_practice_email_template" id="toeic_practice_email_template" rows="10" class="large-text code"><?php echo esc_textarea(get_option('toeic_practice_email_template', $this->getDefaultEmailTemplate())); ?></textarea>
                        <p class="description">
                            <?php echo esc_html__('Email template for test completion notifications. You can use the following placeholders:', 'toeic-practice'); ?>
                            <br>
                            <code>{user_name}</code> - <?php echo esc_html__('User\'s name', 'toeic-practice'); ?><br>
                            <code>{test_title}</code> - <?php echo esc_html__('Test title', 'toeic-practice'); ?><br>
                            <code>{score}</code> - <?php echo esc_html__('Test score', 'toeic-practice'); ?><br>
                            <code>{date}</code> - <?php echo esc_html__('Test completion date', 'toeic-practice'); ?><br>
                            <code>{time_taken}</code> - <?php echo esc_html__('Time taken to complete the test', 'toeic-practice'); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Display advanced settings
     */
    private function displayAdvancedSettings() {
        ?>
        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_data_retention"><?php echo esc_html__('Data Retention Period (days)', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="number" name="toeic_practice_data_retention" id="toeic_practice_data_retention" value="<?php echo esc_attr(get_option('toeic_practice_data_retention', 365)); ?>" class="regular-text" min="0">
                        <p class="description"><?php echo esc_html__('Number of days to keep test results data. Set to 0 to keep indefinitely.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_debug_mode"><?php echo esc_html__('Debug Mode', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="checkbox" name="toeic_practice_debug_mode" id="toeic_practice_debug_mode" value="1" <?php checked(get_option('toeic_practice_debug_mode', 0)); ?>>
                        <p class="description"><?php echo esc_html__('Enable debug mode for troubleshooting.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label><?php echo esc_html__('Reset Plugin Data', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=toeic-practice-settings&tab=advanced&action=reset_data'), 'toeic_practice_reset_data')); ?>" class="button" onclick="return confirm('<?php echo esc_js(__('Are you sure you want to reset all plugin data? This action cannot be undone.', 'toeic-practice')); ?>');">
                            <?php echo esc_html__('Reset All Data', 'toeic-practice'); ?>
                        </a>
                        <p class="description"><?php echo esc_html__('This will delete all tests, questions, and results data. Use with caution!', 'toeic-practice'); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Display Azure settings
     */
    private function displayAzureSettings() {
        ?>
        <table class="form-table" role="presentation">
            <tbody>
                <!-- Speech to Text -->
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_azure_stt_subscription_key"><?php echo esc_html__('Azure Speech to Text Subscription Key', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="toeic_practice_azure_stt_subscription_key" id="toeic_practice_azure_stt_subscription_key" value="<?php echo esc_attr(get_option('toeic_practice_azure_stt_subscription_key', '')); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Azure subscription key for speech to text services.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_azure_stt_service_region"><?php echo esc_html__('Azure Speech to Text Service Region', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="toeic_practice_azure_stt_service_region" id="toeic_practice_azure_stt_service_region" value="<?php echo esc_attr(get_option('toeic_practice_azure_stt_service_region', '')); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Azure service region for speech to text services.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <!-- Translator -->
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_azure_translator_subscription_key"><?php echo esc_html__('Azure Translator Subscription Key', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="toeic_practice_azure_translator_subscription_key" id="toeic_practice_azure_translator_subscription_key" value="<?php echo esc_attr(get_option('toeic_practice_azure_translator_subscription_key', '')); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Azure subscription key for translator services.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="toeic_practice_azure_translator_service_region"><?php echo esc_html__('Azure Translator Service Region', 'toeic-practice'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="toeic_practice_azure_translator_service_region" id="toeic_practice_azure_translator_service_region" value="<?php echo esc_attr(get_option('toeic_practice_azure_translator_service_region', '')); ?>" class="regular-text">
                        <p class="description"><?php echo esc_html__('Azure service region for translator services.', 'toeic-practice'); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Get default email template
     */
    private function getDefaultEmailTemplate() {
        return "Hello {user_name},

Thank you for completing the TOEIC practice test: {test_title}.

Your score: {score}
Date: {date}
Time taken: {time_taken}

Keep practicing to improve your TOEIC score!

Regards,
The TOEIC Practice Team";
    }
}
