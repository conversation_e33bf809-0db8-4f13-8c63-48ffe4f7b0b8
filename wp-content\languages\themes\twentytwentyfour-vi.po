# Translation of Themes - Twenty Twenty-Four in Vietnamese
# This file is distributed under the same license as the Themes - Twenty Twenty-Four package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-08-24 00:45:43+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: vi_VN\n"
"Project-Id-Version: Themes - Twenty Twenty-Four\n"

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four"
msgstr "Twenty Twenty-Four"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to color and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."
msgstr "Twenty Twenty-Four được thiết kế linh hoạt, đa năng và phù hợp với mọi trang web. Bộ sưu tập mẫu và bố cục của theme đáp ứng nhiều nhu cầu khác nhau, chẳng hạn như giới thiệu doanh nghiệp, viết blog, sáng tác nội dung hoặc trưng bày tác phẩm. Vô số khả năng mở ra chỉ với một vài điều chỉnh về màu sắc và kiểu chữ. Twenty Twenty-Four đi kèm với các biến thể phong cách và thiết kế trang đầy đủ giúp tăng tốc quá trình xây dựng website, hoàn toàn tương thích với trình soạn thảo website và tận dụng các công cụ thiết kế mới được giới thiệu trong WordPress 6.4."

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern description"
msgid "A feature grid of 2 rows and 3 columns with headings and text."
msgstr "Lưới tính năng gồm 2 hàng và 3 cột với tiêu đề và văn bản."

#: patterns/gallery-project-layout.php
msgctxt "Pattern description"
msgid "A gallery section with a project layout with 2 images."
msgstr "Phần thư viện ảnh với bố cục dự án với 2 hình ảnh."

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern description"
msgid "A cover image section that covers the entire width."
msgstr "Phần ảnh bìa bao phủ toàn bộ chiều rộng."

#: patterns/cta-pricing.php
msgctxt "Pattern description"
msgid "A pricing section with a title, a paragraph and three pricing levels."
msgstr "Phần định giá với tiêu đề, đoạn văn bản và ba mức giá."

#: patterns/banner-project-description.php
msgctxt "Pattern description"
msgid "Project description section with title, paragraph, and an image."
msgstr "Phần mô tả dự án với tiêu đề, đoạn văn bản và hình ảnh."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern description"
msgid "A title, a paragraph and a CTA button on the left with an image on the right."
msgstr "Tiêu đề, đoạn văn và nút kêu gọi hành động (CTA) ở bên trái, hình ảnh ở bên phải."

#: patterns/text-faq.php:35
msgctxt "Question in the FAQ pattern"
msgid "Who is behind Études?"
msgstr "Ai là người sáng lập Études?"

#: patterns/team-4-col.php
msgctxt "Pattern description"
msgid "A team section, with a heading, a paragraph, and 4 columns for team members."
msgstr "Phần nhóm, với tiêu đề, đoạn văn bản và 4 cột dành cho các thành viên trong nhóm."

#: patterns/posts-list.php
msgctxt "Pattern description"
msgid "A list of posts without images, 1 column."
msgstr "Danh sách bài viết không có hình ảnh, 1 cột."

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 4 columns."
msgstr "Danh sách bài viết chỉ có hình ảnh nổi bật, 4 cột."

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 3 columns."
msgstr "Danh sách bài viết chỉ có hình ảnh nổi bật, 3 cột."

#: patterns/posts-grid-2-col.php
msgctxt "Pattern description"
msgid "A grid of posts featuring the first post, 2 columns."
msgstr "Lưới bài viết nổi bật bài viết đầu tiên, 2 cột."

#: patterns/posts-3-col.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns."
msgstr "Danh sách bài viết, 3 cột."

#: patterns/posts-1-col.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column."
msgstr "Danh sách bài viết, 1 cột."

#: patterns/page-portfolio-overview.php
msgctxt "Pattern description"
msgid "A full portfolio page with a section for project description, project details, a full screen image, and a gallery section with two images."
msgstr "Trang danh mục đầu tư đầy đủ với phần mô tả dự án, chi tiết dự án, hình ảnh toàn màn hình và phần thư viện ảnh với hai hình ảnh."

#: patterns/page-newsletter-landing.php
msgctxt "Pattern description"
msgid "A block with a newsletter subscription CTA for a landing page."
msgstr "Khối với nút kêu gọi hành động (CTA) đăng ký nhận bản tin cho trang đích."

#: patterns/page-home-portfolio.php
msgctxt "Pattern description"
msgid "A portfolio home page with a description and a 4-column post section with only feature images."
msgstr "Trang chủ danh mục đầu tư với phần mô tả và phần bài viết 4 cột chỉ có hình ảnh nổi bật."

#: patterns/page-home-business.php
msgctxt "Pattern description"
msgid "A business home page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Trang chủ doanh nghiệp với phần hero, phần văn bản, phần dịch vụ, phần nhóm, phần khách hàng, phần Câu hỏi thường gặp và phần kêu gọi hành động (CTA)."

#: patterns/page-home-blogging.php
msgctxt "Pattern description"
msgid "A blogging home page with a hero section, a text section, a blog section, and a CTA section."
msgstr "Trang chủ blog với phần hero, phần văn bản, phần blog và phần kêu gọi hành động (CTA)."

#: patterns/page-about-business.php
msgctxt "Pattern description"
msgid "A business about page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Trang giới thiệu doanh nghiệp với phần hero, phần văn bản, phần dịch vụ, phần nhóm, phần khách hàng, phần Câu hỏi thường gặp và phần kêu gọi hành động (CTA)."

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern description"
msgid "A gallery section with 4 columns and offset images."
msgstr "Phần thư viện ảnh với 4 cột và hình ảnh lệch nhau."

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern description"
msgid "A gallery section with 3 columns and offset images."
msgstr "Phần thư viện ảnh với 3 cột và hình ảnh lệch nhau."

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern description"
msgid "A gallery section with 2 columns and offset images."
msgstr "Phần thư viện ảnh với 2 cột và hình ảnh lệch nhau."

#: patterns/footer.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 4 columns."
msgstr "Phần chân trang với colophon và 4 cột."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 3 columns."
msgstr "Phần chân trang với colophon và 3 cột."

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern description"
msgid "A footer section with a centered logo, navigation, and WordPress credits."
msgstr "Phần chân trang với logo được căn giữa, điều hướng và ghi công WordPress."

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern description"
msgid "Subscribers CTA section with a title, a paragraph and a CTA button."
msgstr "Phần kêu gọi hành động (CTA) dành cho người đăng ký với tiêu đề, đoạn văn bản và nút CTA."

#: patterns/cta-services-image-left.php
msgctxt "Pattern description"
msgid "An image, title, paragraph and a CTA button to describe services."
msgstr "Hình ảnh, tiêu đề, đoạn văn bản và nút kêu gọi hành động (CTA) để mô tả dịch vụ."

#: patterns/cta-rsvp.php patterns/page-rsvp-landing.php
msgctxt "Pattern description"
msgid "A large RSVP heading sideways, a description, and a CTA button."
msgstr "Tiêu đề RSVP lớn ở bên cạnh, phần mô tả và nút kêu gọi hành động (CTA)."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern description"
msgid "A title, paragraph, two CTA buttons, and an image for a general CTA section."
msgstr "Tiêu đề, đoạn văn bản, hai nút kêu gọi hành động (CTA) và hình ảnh cho phần CTA chung."

#: patterns/banner-hero.php
msgctxt "Pattern description"
msgid "A hero section with a title, a paragraph, a CTA button, and an image."
msgstr "Phần hero với tiêu đề, đoạn văn bản, nút kêu gọi hành động (CTA) và hình ảnh."

#: patterns/footer.php:50
msgid "History"
msgstr "Lịch sử"

#: patterns/footer.php:96
msgid "Twitter/X"
msgstr "Twitter/X"

#: patterns/footer.php:49
msgid "Team"
msgstr "Đội ngũ"

#: patterns/footer.php:74
msgid "Contact Us"
msgstr "Liên hệ với chúng tôi"

#: patterns/footer.php:73
msgid "Terms and Conditions"
msgstr "Điều khoản và Điều kiện"

#: patterns/footer.php:72
msgid "Privacy Policy"
msgstr "Chính sách Quyền riêng tư"

#: patterns/footer.php:51
msgid "Careers"
msgstr "Nghề nghiệp"

#: patterns/posts-list.php
msgctxt "Pattern title"
msgid "List of posts without images, 1 column"
msgstr "Danh sách bài viết không có hình ảnh, 1 cột"

#: functions.php:200
msgid "A collection of full page layouts."
msgstr "Bộ sưu tập bố cục trang đầy đủ."

#: patterns/hidden-portfolio-hero.php:16
msgid "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."
msgstr "Tôi là <em>Leia Acosta</em>, một nhiếp ảnh gia đam mê, tìm thấy nguồn cảm hứng trong việc nắm bắt vẻ đẹp thoáng qua của cuộc sống."

#: functions.php:199
msgctxt "Block pattern category"
msgid "Pages"
msgstr "Các trang"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Siêu dữ liệu Bài viết"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Thanh bên"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Chân trang"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Đầu trang"

#: theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to white"
msgstr "Dọc xám chì đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard mint to white"
msgstr "Dọc bạc hà đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sage to white"
msgstr "Dọc xanh xám đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard rust to white"
msgstr "Dọc nâu đỏ đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sandstone to white"
msgstr "Dọc cát kết đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard beige to white"
msgstr "Dọc be đậm đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to white"
msgstr "Dọc xám chì nhạt đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft mint to white"
msgstr "Dọc bạc hà nhạt đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sage to white"
msgstr "Dọc xanh xám nhạt đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft rust to white"
msgstr "Dọc nâu đỏ nhạt đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sandstone to white"
msgstr "Dọc cát kết nhạt đến trắng"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft beige to white"
msgstr "Dọc be nhạt đến trắng"

#: theme.json
msgctxt "Duotone name"
msgid "Black and pastel blue"
msgstr "Đen và xanh pastel"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sage"
msgstr "Đen và xanh xám"

#: theme.json
msgctxt "Duotone name"
msgid "Black and rust"
msgstr "Đen và nâu đỏ"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sandstone"
msgstr "Đen và cát kết"

#: theme.json
msgctxt "Duotone name"
msgid "Black and white"
msgstr "Đen và trắng"

#: styles/rust.json
msgctxt "Color name"
msgid "Base / 2"
msgstr "Nền / 2"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard rust to beige"
msgstr "Dọc nâu đỏ đậm đến be"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical rust to beige"
msgstr "Dọc nâu đỏ đến be"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard transparent rust to beige"
msgstr "Dọc nâu đỏ trong suốt đậm đến be"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical transparent rust to beige"
msgstr "Dọc nâu đỏ trong suốt đến be"

#: styles/rust.json
msgctxt "Duotone name"
msgid "Dark rust to beige"
msgstr "Nâu đỏ đậm đến be"

#: styles/rust.json
msgctxt "Style variation name"
msgid "Rust"
msgstr "Nâu đỏ"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Five"
msgstr "Nổi bật / Năm"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Four"
msgstr "Nổi bật / Bốn"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Three"
msgstr "Nổi bật / Ba"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Two"
msgstr "Nổi bật / Hai"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent"
msgstr "Nổi bật"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to dark gray"
msgstr "Dọc xám chì đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard steel to dark gray"
msgstr "Dọc thép đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard olive to dark gray"
msgstr "Dọc ô liu đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard cinnamon to dark gray"
msgstr "Dọc quế đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard walnut to dark gray"
msgstr "Dọc óc chó đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard beige to dark gray"
msgstr "Dọc be đậm đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to dark gray"
msgstr "Dọc xám chì nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft steel to dark gray"
msgstr "Dọc thép nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft olive to dark gray"
msgstr "Dọc ô liu nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft cinnamon to dark gray"
msgstr "Dọc quế nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft walnut to dark gray"
msgstr "Dọc óc chó nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft driftwood to dark gray"
msgstr "Dọc gỗ lũa nhạt đến xám đậm"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and steel"
msgstr "Xám đậm và thép"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and olive"
msgstr "Xám đậm và ô liu"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and cinnamon"
msgstr "Xám đậm và quế"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and walnut"
msgstr "Xám đậm và óc chó"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and white"
msgstr "Xám đậm và trắng"

#: styles/onyx.json
msgctxt "Style variation name"
msgid "Onyx"
msgstr "Onyx"

#: styles/mint.json
msgctxt "Style variation name"
msgid "Mint"
msgstr "Bạc hà"

#: styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 3"
msgstr "Tương phản / 3"

#: styles/maelstrom.json
msgctxt "Style variation name"
msgid "Maelstrom"
msgstr "Maelstrom"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ocean"
msgstr "Dọc mực đậm đến đại dương"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to slate"
msgstr "Dọc đại dương đậm đến đá phiến"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ice"
msgstr "Dọc mực đậm đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to ice"
msgstr "Dọc đại dương đậm đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard slate to ice"
msgstr "Dọc đá phiến đậm đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ice to azure"
msgstr "Dọc băng đậm đến xanh da trời"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ocean"
msgstr "Dọc mực đến đại dương"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to slate"
msgstr "Dọc đại dương đến đá phiến"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ice"
msgstr "Dọc mực đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to ice"
msgstr "Dọc đại dương đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical slate to ice"
msgstr "Dọc đá phiến đến băng"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical azure to ice"
msgstr "Dọc xanh da trời đến băng"

#: styles/ice.json
msgctxt "Style variation name"
msgid "Ice"
msgstr "Băng"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Lớn"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Trung bình"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Nhỏ"

#: styles/fossil.json styles/maelstrom.json theme.json
msgctxt "Font family name"
msgid "Cardo"
msgstr "Cardo"

#: styles/fossil.json styles/ice.json theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Three"
msgstr "Tương phản / Ba"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Two"
msgstr "Tương phản / Hai"

#: styles/fossil.json
msgctxt "Style variation name"
msgid "Fossil"
msgstr "Hóa thạch"

#: styles/ember.json styles/ice.json styles/maelstrom.json styles/mint.json
msgctxt "Font family name"
msgid "Jost"
msgstr "Jost"

#: styles/ember.json styles/mint.json
msgctxt "Font family name"
msgid "Instrument Sans"
msgstr "Instrument Sans"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json theme.json
msgctxt "Color name"
msgid "Base / Two"
msgstr "Nền / Hai"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Tương phản"

#: styles/ember.json styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 2"
msgstr "Tương phản / 2"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Nền"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to sable"
msgstr "Dọc mun đậm đến nâu sẫm"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to taupe"
msgstr "Dọc nâu sẫm đậm đến nâu xám"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to beige"
msgstr "Dọc mun đậm đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to beige"
msgstr "Dọc nâu sẫm đậm đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard taupe to beige"
msgstr "Dọc nâu xám đậm đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard beige to linen"
msgstr "Dọc be đậm đến vải lanh"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to sable"
msgstr "Dọc mun đến nâu sẫm"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to beige"
msgstr "Dọc mun đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical sable to beige"
msgstr "Dọc nâu sẫm đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical taupe to beige"
msgstr "Dọc nâu xám đến be"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical linen to beige"
msgstr "Dọc vải lanh đến be"

#: styles/ember.json
msgctxt "Duotone name"
msgid "Orange and white"
msgstr "Cam và trắng"

#: styles/ember.json
msgctxt "Style variation name"
msgid "Ember"
msgstr "Ember"

#: patterns/text-title-left-image-right.php:35
msgctxt "Call to Action button text"
msgid "About us"
msgstr "Về chúng tôi"

#: patterns/text-title-left-image-right.php:28
msgctxt "Description for the About pattern"
msgid "Leaving an indelible mark on the landscape of tomorrow."
msgstr "Để lại dấu ấn không thể phai mờ trên bối cảnh của ngày mai."

#: patterns/text-project-details.php
msgctxt "Pattern title"
msgid "Project details"
msgstr "Chi tiết dự án"

#: patterns/text-feature-grid-3-col.php:112
msgctxt "Sample content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Trải nghiệm sự kết hợp giữa trí tưởng tượng và chuyên môn với Giải pháp Kiến trúc Études."

#: patterns/text-feature-grid-3-col.php:84
msgctxt "Sample feature heading"
msgid "Consulting"
msgstr "Tư vấn"

#: patterns/text-feature-grid-3-col.php:63
msgctxt "Sample feature heading"
msgid "App Access"
msgstr "Truy cập Ứng dụng"

#: patterns/text-feature-grid-3-col.php:51
msgctxt "Sample feature heading"
msgid "Continuous Support"
msgstr "Hỗ trợ Liên tục"

#: patterns/text-feature-grid-3-col.php:43
#: patterns/text-feature-grid-3-col.php:55
#: patterns/text-feature-grid-3-col.php:67
#: patterns/text-feature-grid-3-col.php:88
#: patterns/text-feature-grid-3-col.php:100
msgctxt "Sample feature content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Trải nghiệm sự kết hợp giữa trí tưởng tượng và chuyên môn với Giải pháp Kiến trúc Études."

#: patterns/text-feature-grid-3-col.php:16
msgctxt "Heading of the features"
msgid "A passion for creating spaces"
msgstr "Niềm đam mê tạo ra không gian"

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern title"
msgid "Feature grid, 3 columns"
msgstr "Lưới tính năng, 3 cột"

#: patterns/text-faq.php:57
msgctxt "Question in the FAQ pattern"
msgid "Can I apply to be a part of the team or work as a contractor?"
msgstr "Tôi có thể nộp đơn xin trở thành một phần của nhóm hoặc làm việc như một nhà thầu không?"

#: patterns/text-faq.php:24
msgctxt "Question in the FAQ pattern"
msgid "What is your process working in smaller projects?"
msgstr "Quy trình làm việc của bạn trong các dự án nhỏ hơn là gì?"

#: patterns/text-faq.php:15
msgctxt "Heading of the FAQs"
msgid "FAQs"
msgstr "Câu hỏi thường gặp"

#: patterns/text-faq.php:12
msgctxt "Name of the FAQ pattern"
msgid "FAQs"
msgstr "Câu hỏi thường gặp"

#: patterns/text-faq.php
msgctxt "Pattern title"
msgid "FAQ"
msgstr "Câu hỏi thường gặp"

#. Translators: About text placeholder
#: patterns/text-centered-statement-small.php:23
msgid "I write about finance, management and economy, my book “%1$s” is out now."
msgstr "Tôi viết về tài chính, quản lý và kinh tế, cuốn sách “%1$s” của tôi hiện đã được xuất bản."

#. Translators: About link placeholder
#: patterns/text-centered-statement-small.php:20
msgid "Money Studies"
msgstr "Nghiên cứu Tiền tệ"

#: patterns/text-alternating-images.php:105
msgctxt "Sample list item"
msgid "Exclusive access to design insights."
msgstr "Truy cập độc quyền vào thông tin chi tiết về thiết kế."

#: patterns/text-alternating-images.php:91
msgctxt "Sample heading"
msgid "Études Newsletter"
msgstr "Bản tin Études"

#: patterns/text-alternating-images.php:82
msgid "Windows of a building in Nuremberg, Germany"
msgstr "Cửa sổ của một tòa nhà ở Nuremberg, Đức"

#: patterns/text-alternating-images.php:64
msgid "Tourist taking photo of a building"
msgstr "Khách du lịch chụp ảnh một tòa nhà"

#: patterns/text-alternating-images.php:37
msgctxt "Sample list heading"
msgid "Études Architect App"
msgstr "Ứng dụng Kiến trúc sư Études"

#: patterns/text-alternating-images.php
msgctxt "Pattern title"
msgid "Text with alternating images"
msgstr "Văn bản với hình ảnh xen kẽ"

#: patterns/testimonial-centered.php:39
msgctxt "Designation of Person Provided Testimonial"
msgid "CEO, Greenprint"
msgstr "Giám đốc điều hành, Greenprint"

#: patterns/testimonial-centered.php:35
msgctxt "Name of Person Provided the Testimonial"
msgid "Annie Steiner"
msgstr "Annie Steiner"

#: patterns/testimonial-centered.php:26
msgctxt "Name of testimonial citation group"
msgid "Testimonial source"
msgstr "Nguồn lời chứng thực"

#: patterns/testimonial-centered.php:18
msgctxt "Testimonial Text or Review Text Got From the Person"
msgid "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"
msgstr "“Études đã giúp chúng tôi tiết kiệm hàng nghìn giờ làm việc và đã khai mở những hiểu biết mà chúng tôi chưa bao giờ nghĩ là có thể.”"

#: patterns/testimonial-centered.php:12
msgctxt "Name of testimonial pattern"
msgid "Testimonial"
msgstr "Lời chứng thực"

#: patterns/testimonial-centered.php
msgctxt "Pattern title"
msgid "Centered testimonial"
msgstr "Lời chứng thực trung tâm"

#: patterns/template-search-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio search template"
msgstr "Mẫu tìm kiếm danh mục đầu tư"

#: patterns/template-index-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio index template"
msgstr "Mẫu chỉ mục danh mục đầu tư"

#: patterns/template-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home template with post featured images"
msgstr "Mẫu trang chủ danh mục đầu tư với hình ảnh nổi bật bài đăng"

#: patterns/template-home-business.php
msgctxt "Pattern title"
msgid "Business home template"
msgstr "Mẫu trang chủ doanh nghiệp"

#: patterns/template-archive-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio archive template"
msgstr "Mẫu lưu trữ danh mục đầu tư"

#: patterns/team-4-col.php:116
msgctxt "Sample name of a team member"
msgid "Ivan Lawrence"
msgstr "Ivan Lawrence"

#: patterns/team-4-col.php:97
msgctxt "Sample role of a team member"
msgid "Architect"
msgstr "Kiến trúc sư"

#: patterns/team-4-col.php:92
msgctxt "Sample name of a team member"
msgid "Helga Steiner"
msgstr "Helga Steiner"

#: patterns/team-4-col.php:73
msgctxt "Sample role of a team member"
msgid "Engineering Manager"
msgstr "Quản lý Kỹ thuật"

#: patterns/team-4-col.php:68
msgctxt "Sample name of a team member"
msgid "Rhye Moore"
msgstr "Rhye Moore"

#: patterns/team-4-col.php:49
msgctxt "Sample role of a team member"
msgid "Founder, CEO & Architect"
msgstr "Người sáng lập, Giám đốc điều hành & Kiến trúc sư"

#: patterns/team-4-col.php:44
msgctxt "Sample name of a team member"
msgid "Francesca Piovani"
msgstr "Francesca Piovani"

#: patterns/team-4-col.php:16
msgctxt "Sample heading for the team pattern"
msgid "Meet our team"
msgstr "Gặp gỡ đội ngũ của chúng tôi"

#: patterns/team-4-col.php:11
msgctxt "Name of team pattern"
msgid "Team members"
msgstr "Thành viên nhóm"

#: patterns/team-4-col.php
msgctxt "Pattern title"
msgid "Team members, 4 columns"
msgstr "Thành viên nhóm, 4 cột"

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern title"
msgid "Offset posts with featured images only, 4 columns"
msgstr "Bài viết lệch với chỉ hình ảnh nổi bật, 4 cột"

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern title"
msgid "Posts with featured images only, 3 columns"
msgstr "Bài viết chỉ có hình ảnh nổi bật, 3 cột"

#: patterns/posts-grid-2-col.php:14 patterns/posts-list.php:14
#: patterns/template-index-blogging.php:16
msgid "Watch, Read, Listen"
msgstr "Xem, Đọc, Nghe"

#: patterns/posts-grid-2-col.php
msgctxt "Pattern title"
msgid "Grid of posts featuring the first post, 2 columns"
msgstr "Lưới bài viết nổi bật bài viết đầu tiên, 2 cột"

#: patterns/posts-3-col.php
msgctxt "Pattern title"
msgid "List of posts, 3 columns"
msgstr "Danh sách bài viết, 3 cột"

#: patterns/posts-1-col.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Danh sách bài viết, 1 cột"

#: patterns/page-rsvp-landing.php:49
msgid "Green staircase at Western University, London, Canada"
msgstr "Cầu thang xanh tại Đại học Western, London, Canada"

#: patterns/page-rsvp-landing.php:14
msgctxt "Name of RSVP landing page pattern"
msgid "RSVP Landing Page"
msgstr "Trang đích RSVP"

#: patterns/page-rsvp-landing.php
msgctxt "Pattern title"
msgid "RSVP landing"
msgstr "Trang đích RSVP"

#: patterns/page-portfolio-overview.php
msgctxt "Pattern title"
msgid "Portfolio project overview"
msgstr "Tổng quan dự án danh mục đầu tư"

#: patterns/page-newsletter-landing.php:40
msgctxt "Sample content for newsletter subscribe button"
msgid "Sign up"
msgstr "Đăng ký"

#: patterns/page-newsletter-landing.php:29
msgctxt "sample content for newsletter subscription"
msgid "Subscribe to the newsletter and stay connected with our community"
msgstr "Đăng ký nhận bản tin và kết nối với cộng đồng của chúng tôi"

#: patterns/page-newsletter-landing.php
msgctxt "Pattern title"
msgid "Newsletter landing"
msgstr "Trang đích bản tin"

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern title"
msgid "Portfolio home image gallery"
msgstr "Thư viện ảnh trang chủ danh mục đầu tư"

#: patterns/page-home-business.php
msgctxt "Pattern title"
msgid "Business home"
msgstr "Trang chủ doanh nghiệp"

#: patterns/page-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home"
msgstr "Trang chủ blog"

#: patterns/page-about-business.php
msgctxt "Pattern title"
msgid "About"
msgstr "Giới thiệu"

#: patterns/hidden-sidebar.php:75
msgctxt "search form placeholder"
msgid "Search..."
msgstr "Tìm kiếm..."

#: patterns/hidden-sidebar.php:60
msgid "Financial apps for families"
msgstr "Ứng dụng tài chính cho gia đình"

#: patterns/hidden-sidebar.php:59
msgid "Latest inflation report"
msgstr "Báo cáo lạm phát mới nhất"

#: patterns/hidden-sidebar.php:53
msgid "Links I found useful and wanted to share."
msgstr "Các liên kết tôi thấy hữu ích và muốn chia sẻ."

#: patterns/hidden-sidebar.php:49
msgid "Useful Links"
msgstr "Liên kết hữu ích"

#: patterns/hidden-sidebar.php:33
msgid "Popular Categories"
msgstr "Danh mục phổ biến"

#: patterns/hidden-sidebar.php:17
msgid "About the author"
msgstr "Về tác giả"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Thanh bên"

#: patterns/hidden-search.php:9
msgctxt "search button text"
msgid "Search"
msgstr "Tìm kiếm"

#: patterns/hidden-search.php:9 patterns/hidden-sidebar.php:75
msgctxt "search form label"
msgid "Search"
msgstr "Tìm kiếm"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Tìm kiếm"

#: patterns/hidden-post-navigation.php:12
msgctxt "Label before the title of the next post. There is a space after the colon."
msgid "Next: "
msgstr "Tiếp theo: "

#: patterns/hidden-post-navigation.php:11
msgctxt "Label before the title of the previous post. There is a space after the colon."
msgid "Previous: "
msgstr "Trước đó: "

#: patterns/hidden-post-navigation.php:9 patterns/hidden-post-navigation.php:10
#: patterns/hidden-posts-heading.php:10
#: patterns/template-index-portfolio.php:16
msgid "Posts"
msgstr "Bài viết"

#: patterns/hidden-post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Điều hướng bài viết"

#: patterns/hidden-post-meta.php:25
msgctxt "Prefix for the post category block: in category name"
msgid "in "
msgstr "trong "

#: patterns/hidden-post-meta.php:20
msgctxt "Prefix for the post author block: By author name"
msgid "by"
msgstr "bởi"

#: patterns/hidden-post-meta.php
msgctxt "Pattern title"
msgid "Post meta"
msgstr "Siêu dữ liệu bài viết"

#: patterns/hidden-no-results.php:9
msgctxt "Message explaining that there are no results returned from a search"
msgid "No posts were found."
msgstr "Không tìm thấy bài viết nào."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "No results"
msgstr "Không có kết quả"

#: patterns/hidden-comments.php:12
msgid "Comments"
msgstr "Bình luận"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Bình luận"

#: patterns/hidden-404.php:10
msgctxt "Heading for a webpage that is not found"
msgid "Page Not Found"
msgstr "Không tìm thấy trang"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/gallery-project-layout.php:54
msgid "Art Gallery of Ontario, Toronto, Canada"
msgstr "Bảo tàng Nghệ thuật Ontario, Toronto, Canada"

#: patterns/gallery-project-layout.php:21
msgid "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"
msgstr "Một cầu thang trống dưới mái nhà góc cạnh ở Darling Harbour, Sydney, Úc"

#: patterns/gallery-project-layout.php
msgctxt "Pattern title"
msgid "Project layout"
msgstr "Bố cục dự án"

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 4 columns"
msgstr "Thư viện ảnh lệch, 4 cột"

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 3 columns"
msgstr "Thư viện ảnh lệch, 3 cột"

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 2 columns"
msgstr "Thư viện ảnh lệch, 2 cột"

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern title"
msgid "Full screen image"
msgstr "Hình ảnh toàn màn hình"

#: patterns/footer.php:64 patterns/footer.php:70
msgid "Privacy"
msgstr "Riêng tư"

#: patterns/footer.php:41 patterns/footer.php:47
msgid "About"
msgstr "Giới thiệu"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer with colophon, 4 columns"
msgstr "Chân trang có colophon, 4 cột"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:94
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:95
msgid "Instagram"
msgstr "Instagram"

#: patterns/footer-colophon-3-col.php:57
msgid "Follow"
msgstr "Theo dõi"

#: patterns/footer-colophon-3-col.php:42
msgctxt "Example email in site footer"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/footer-colophon-3-col.php:39
msgid "Contact"
msgstr "Liên hệ"

#: patterns/footer-colophon-3-col.php:30
msgid "Keep up, get in touch."
msgstr "Cập nhật thông tin, hãy liên hệ."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern title"
msgid "Footer with colophon, 3 columns"
msgstr "Chân trang có colophon, 3 cột"

#. Translators: Designed with WordPress
#: patterns/footer-centered-logo-nav.php:25
#: patterns/footer-colophon-3-col.php:94 patterns/footer.php:120
msgid "Designed with %1$s"
msgstr "Được thiết kế với %1$s"

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern title"
msgid "Footer with centered logo and navigation"
msgstr "Chân trang có logo và điều hướng ở giữa"

#: patterns/cta-subscribe-centered.php:31
msgctxt "Sample text for Sign Up Button"
msgid "Sign up"
msgstr "Đăng ký"

#: patterns/cta-subscribe-centered.php:20
msgctxt "Sample text for Subscriber Heading with numbers"
msgid "Join 900+ subscribers"
msgstr "Tham gia cùng hơn 900 người đăng ký"

#: patterns/cta-services-image-left.php:39
msgctxt "Sample button text to view the services"
msgid "Our services"
msgstr "Dịch vụ của chúng tôi"

#: patterns/cta-services-image-left.php:28
msgctxt "Sample heading of the services pattern"
msgid "Guiding your business through the project"
msgstr "Hướng dẫn doanh nghiệp của bạn trong suốt dự án"

#: patterns/cta-services-image-left.php
msgctxt "Pattern title"
msgid "Services call to action with image on left"
msgstr "Dịch vụ kêu gọi hành động với hình ảnh bên trái"

#: patterns/cta-rsvp.php:50 patterns/text-title-left-image-right.php:51
msgid "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"
msgstr "Một đoạn đường nối dọc theo bức tường cong trong Bảo tàng Kiasma, Helsinki, Phần Lan"

#: patterns/cta-rsvp.php:34 patterns/page-rsvp-landing.php:34
msgctxt "Call to action button text for the reservation button"
msgid "Reserve your spot"
msgstr "Đặt chỗ của bạn"

#: patterns/cta-rsvp.php:27 patterns/page-rsvp-landing.php:28
msgctxt "RSVP call to action description"
msgid "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."
msgstr "Trải nghiệm sự kết hợp giữa trí tưởng tượng và chuyên môn với Hội nghị thượng đỉnh Études Arch, tháng 2 năm 2025."

#: patterns/cta-rsvp.php:21 patterns/page-rsvp-landing.php:23
msgctxt "Initials for ´please respond´"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php:11
msgctxt "Name of RSVP pattern"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php
msgctxt "Pattern title"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-pricing.php:203
msgctxt "Button text for the third pricing level"
msgid "Subscribe"
msgstr "Đăng ký"

#: patterns/cta-pricing.php:189
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android"
msgstr "Truy cập độc quyền vào ứng dụng <em>Études</em> cho iOS và Android"

#: patterns/cta-pricing.php:173
msgctxt "Feature for pricing level"
msgid "Exclusive, unlimited access to <em>Études Articles</em>."
msgstr "Truy cập độc quyền, không giới hạn vào <em>Études Articles</em>."

#: patterns/cta-pricing.php:162
msgctxt "Sample price for the third pricing level"
msgid "$28"
msgstr "$28"

#: patterns/cta-pricing.php:157
msgctxt "Sample heading for the third pricing level"
msgid "Expert"
msgstr "Chuyên gia"

#: patterns/cta-pricing.php:145
msgctxt "Button text for the second pricing level"
msgid "Subscribe"
msgstr "Đăng ký"

#: patterns/cta-pricing.php:115
msgctxt "Feature for pricing level"
msgid "Access to 20 exclusive <em>Études Articles</em> per month."
msgstr "Truy cập vào 20 <em>Études Articles</em> độc quyền mỗi tháng."

#: patterns/cta-pricing.php:104
msgctxt "Sample price for the second pricing level"
msgid "$12"
msgstr "$12"

#: patterns/cta-pricing.php:99
msgctxt "Sample heading for the second pricing level"
msgid "Connoisseur"
msgstr "Người sành sỏi"

#: patterns/cta-pricing.php:87
msgctxt "Button text for the first pricing level"
msgid "Subscribe"
msgstr "Đăng ký"

#: patterns/cta-pricing.php:72 patterns/cta-pricing.php:131
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android."
msgstr "Truy cập độc quyền vào ứng dụng <em>Études</em> cho iOS và Android."

#: patterns/cta-pricing.php:62 patterns/cta-pricing.php:123
#: patterns/cta-pricing.php:181
msgctxt "Feature for pricing level"
msgid "Weekly print edition."
msgstr "Ấn bản in hàng tuần."

#: patterns/cta-pricing.php:53
msgctxt "Feature for pricing level"
msgid "Access to 5 exclusive <em>Études Articles</em> per month."
msgstr "Truy cập vào 5 <em>Études Articles</em> độc quyền mỗi tháng."

#: patterns/cta-pricing.php:42
msgctxt "Sample price for the first pricing level"
msgid "$0"
msgstr "$0"

#: patterns/cta-pricing.php:37
msgctxt "Sample heading for the first pricing level"
msgid "Free"
msgstr "Miễn phí"

#: patterns/cta-pricing.php:22
msgctxt "Sample description for a pricing table"
msgid "We offer flexible options, which you can adapt to the different needs of each project."
msgstr "Chúng tôi cung cấp các tùy chọn linh hoạt, bạn có thể điều chỉnh cho phù hợp với nhu cầu khác nhau của từng dự án."

#: patterns/cta-pricing.php:18
msgctxt "Sample heading for pricing pattern"
msgid "Our Services"
msgstr "Dịch vụ của chúng tôi"

#: patterns/cta-pricing.php:11
msgctxt "Name for the pricing pattern"
msgid "Pricing Table"
msgstr "Bảng giá"

#: patterns/cta-pricing.php
msgctxt "Pattern title"
msgid "Pricing"
msgstr "Bảng giá"

#: patterns/cta-content-image-on-right.php:59
#: patterns/cta-services-image-left.php:19
msgid "White abstract geometric artwork from Dresden, Germany"
msgstr "Tác phẩm nghệ thuật hình học trừu tượng màu trắng từ Dresden, Đức"

#: patterns/cta-content-image-on-right.php:47
msgctxt "Button text of this section"
msgid "How it works"
msgstr "Cách thức hoạt động"

#: patterns/cta-content-image-on-right.php:41
msgctxt "Button text of this section"
msgid "Download app"
msgstr "Tải xuống ứng dụng"

#: patterns/cta-content-image-on-right.php:28
#: patterns/text-alternating-images.php:48
msgctxt "Sample list item"
msgid "Showcase your projects."
msgstr "Trưng bày các dự án của bạn."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern title"
msgid "Call to action with image on right"
msgstr "Kêu gọi hành động với hình ảnh bên phải"

#: patterns/banner-project-description.php:41
msgid "Hyatt Regency San Francisco, San Francisco, United States"
msgstr "Hyatt Regency San Francisco, San Francisco, Hoa Kỳ"

#: patterns/banner-project-description.php:26
msgctxt "Sample descriptive text for a project or post."
msgid "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."
msgstr "Dự án chuyển đổi này nhằm mục đích nâng cao cơ sở hạ tầng, khả năng tiếp cận và không gian triển lãm của phòng trưng bày đồng thời bảo tồn di sản văn hóa phong phú của nó."

#: patterns/banner-project-description.php
msgctxt "Pattern title"
msgid "Project description"
msgstr "Mô tả dự án"

#: patterns/banner-hero.php:52
msgid "Building exterior in Toronto, Canada"
msgstr "Ngoại thất tòa nhà ở Toronto, Canada"

#: patterns/banner-hero.php:37
msgctxt "Button text of the hero section"
msgid "About us"
msgstr "Về chúng tôi"

#: patterns/banner-hero.php:18
msgctxt "Heading of the hero section"
msgid "A commitment to innovation and sustainability"
msgstr "Cam kết đổi mới và bền vững"

#: functions.php:111
msgid "With asterisk"
msgstr "Có dấu sao"

#: functions.php:93
msgid "With arrow"
msgstr "Có mũi tên"

#: functions.php:74
msgid "Checkmark"
msgstr "Dấu tích"

#: functions.php:51
msgid "Pill"
msgstr "Viên thuốc"

#: functions.php:28
msgid "Arrow icon"
msgstr "Biểu tượng mũi tên"

#. Author URI of the theme
#. Translators: WordPress link.
#: style.css patterns/footer-centered-logo-nav.php:22
#: patterns/footer-colophon-3-col.php:91 patterns/footer.php:117
msgid "https://wordpress.org"
msgstr "https://wordpress.org"

#: patterns/footer-colophon-3-col.php:82
msgid "&copy;"
msgstr "&copy;"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Cực lớn"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Rất lớn"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Serif"
msgstr "Phông chữ Serif Hệ thống"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Sans-serif"
msgstr "Phông chữ Sans-serif Hệ thống"

#: patterns/text-title-left-image-right.php:21
msgctxt "Headline for the About pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études cung cấp các giải pháp tư vấn, quản lý, thiết kế và nghiên cứu toàn diện. Mỗi nỗ lực kiến trúc là một cơ hội để định hình tương lai."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern title"
msgid "Title text and button on left with image on right"
msgstr "Văn bản tiêu đề và nút ở bên trái, hình ảnh ở bên phải"

#: patterns/text-project-details.php:35 patterns/text-project-details.php:43
msgctxt "Descriptive text for the feature area"
msgid "The revitalized Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."
msgstr "Phòng trưng bày nghệ thuật được hồi sinh sẽ tái định nghĩa bối cảnh văn hóa của Toronto, đóng vai trò là một trung tâm kết nối giữa biểu hiện nghệ thuật, sự tham gia của cộng đồng và kỳ quan kiến trúc. Dự án mở rộng và cải tạo bày tỏ lòng kính trọng đối với lịch sử phong phú của Phòng trưng bày Nghệ thuật đồng thời hướng tới tương lai, đảm bảo rằng phòng trưng bày vẫn là ngọn hải đăng của nguồn cảm hứng."

#: patterns/text-project-details.php:27
msgctxt "Descriptive title for the feature area"
msgid "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."
msgstr "Với sự chú ý tỉ mỉ đến từng chi tiết và cam kết với sự xuất sắc, chúng tôi tạo ra những không gian truyền cảm hứng, nâng cao và làm phong phú thêm cuộc sống của những người sử dụng chúng."

#: patterns/text-project-details.php:18
msgctxt "Title text for the feature area"
msgid "The revitalized art gallery is set to redefine cultural landscape."
msgstr "Phòng trưng bày nghệ thuật được hồi sinh sẽ tái định nghĩa bối cảnh văn hóa."

#: patterns/text-feature-grid-3-col.php:39
msgctxt "Sample feature heading"
msgid "Renovation and restoration"
msgstr "Cải tạo và Phục hồi"

#: patterns/text-feature-grid-3-col.php:24
msgctxt "Sub-heading of the features"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Bộ dịch vụ chuyên nghiệp toàn diện của chúng tôi phục vụ đa dạng khách hàng, từ chủ nhà đến nhà phát triển thương mại."

#: patterns/text-faq.php:46
msgctxt "Question in the FAQ pattern"
msgid "I'd like to get to meet fellow architects, how can I do that?"
msgstr "Tôi muốn gặp gỡ các kiến trúc sư đồng nghiệp, tôi có thể làm điều đó như thế nào?"

#: patterns/text-faq.php:27 patterns/text-faq.php:38 patterns/text-faq.php:49
#: patterns/text-faq.php:60
msgctxt "Answer in the FAQ pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études cung cấp các giải pháp tư vấn, quản lý, thiết kế và nghiên cứu toàn diện. Tầm nhìn của chúng tôi là đi đầu trong đổi mới kiến trúc, thúc đẩy một cộng đồng kiến trúc sư và những người đam mê toàn cầu, đoàn kết bởi niềm đam mê tạo ra không gian. Mỗi nỗ lực kiến trúc là một cơ hội để định hình tương lai."

#: patterns/text-centered-statement.php:21
msgid "<em>Études</em> is not confined to the past—we are passionate about the cutting edge designs shaping our world today."
msgstr "<em>Études</em> không bị giới hạn bởi quá khứ—chúng tôi đam mê những thiết kế tiên tiến đang định hình thế giới ngày nay."

#: patterns/text-centered-statement.php
msgctxt "Pattern title"
msgid "Centered statement"
msgstr "Câu khẳng định trung tâm"

#: patterns/text-centered-statement-small.php
msgctxt "Pattern title"
msgid "Centered statement, small"
msgstr "Câu khẳng định trung tâm, nhỏ"

#: patterns/text-alternating-images.php:101
msgctxt "Sample list item"
msgid "Case studies that celebrate architecture."
msgstr "Các nghiên cứu điển hình tôn vinh kiến trúc."

#: patterns/text-alternating-images.php:97
msgctxt "Sample list item"
msgid "A world of thought-provoking articles."
msgstr "Thế giới bài viết kích thích tư duy."

#: patterns/text-alternating-images.php:23
msgctxt "Sample subheading content"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Bộ dịch vụ chuyên nghiệp toàn diện của chúng tôi phục vụ đa dạng khách hàng, từ chủ nhà đến nhà phát triển thương mại."

#: patterns/text-alternating-images.php:19
msgctxt "Sample heading content"
msgid "An array of resources"
msgstr "Hàng loạt tài nguyên"

#: patterns/template-single-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio single post template"
msgstr "Mẫu bài đăng đơn danh mục đầu tư"

#: patterns/template-search-blogging.php
msgctxt "Pattern title"
msgid "Blogging search template"
msgstr "Mẫu tìm kiếm blog"

#: patterns/template-index-blogging.php
msgctxt "Pattern title"
msgid "Blogging index template"
msgstr "Mẫu chỉ mục blog"

#: patterns/template-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home template"
msgstr "Mẫu trang chủ blog"

#: patterns/template-archive-blogging.php
msgctxt "Pattern title"
msgid "Blogging archive template"
msgstr "Mẫu lưu trữ blog"

#: patterns/team-4-col.php:121
msgctxt "Sample role of a team member"
msgid "Project Manager"
msgstr "Quản lý Dự án"

#: patterns/team-4-col.php:20
msgctxt "Sample descriptive text of the team pattern"
msgid "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."
msgstr "Đội ngũ chuyên gia toàn diện của chúng tôi phục vụ một nhóm đa dạng, từ các kiến trúc sư dày dạn kinh nghiệm đến các kỹ sư nổi tiếng."

#: patterns/page-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home with post featured images"
msgstr "Trang chủ danh mục đầu tư với hình ảnh nổi bật bài đăng"

#: patterns/hidden-sidebar.php:72
msgid "Search the website"
msgstr "Tìm kiếm trên trang web"

#: patterns/hidden-portfolio-hero.php
msgctxt "Pattern title"
msgid "Portfolio hero"
msgstr "Hero danh mục đầu tư"

#: patterns/hidden-404.php:13
msgctxt "Message to convey that a webpage could not be found"
msgid "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."
msgstr "Trang bạn đang tìm kiếm không tồn tại hoặc đã được chuyển đi. Vui lòng thử tìm kiếm bằng biểu mẫu bên dưới."

#: patterns/gallery-project-layout.php:49
msgctxt "Sample text for the feature area"
msgid "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."
msgstr "2. Các nghiên cứu điển hình tôn vinh nghệ thuật có thể khơi dậy sự tò mò và truyền cảm hứng."

#: patterns/gallery-project-layout.php:38
msgctxt "Sample text for the feature area"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."
msgstr "Bộ dịch vụ chuyên nghiệp toàn diện của chúng tôi phục vụ đa dạng khách hàng, từ chủ nhà đến nhà phát triển thương mại. Với cam kết đổi mới và bền vững, Études là cầu nối biến những giấc mơ kiến trúc thành hiện thực được xây dựng đáng chú ý."

#: patterns/gallery-project-layout.php:26
msgctxt "Sample text for the feature area"
msgid "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."
msgstr "1. Thông qua Études, chúng tôi khao khát tái định nghĩa ranh giới kiến trúc và mở ra một kỷ nguyên mới về sự xuất sắc trong thiết kế, để lại dấu ấn không thể phai mờ trong môi trường xây dựng."

#: patterns/footer.php:92
msgid "Social Media"
msgstr "Mạng xã hội"

#: patterns/footer.php:86
msgid "Social"
msgstr "Kết nối"

#: patterns/cta-subscribe-centered.php:24
msgctxt "Sample text for Subscriber Description"
msgid "Stay in the loop with everything you need to know."
msgstr "Luôn cập nhật mọi thứ bạn cần biết."

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern title"
msgid "Centered call to action"
msgstr "Kêu gọi hành động trung tâm"

#: patterns/cta-services-image-left.php:32
msgctxt "Sample description of the services pattern"
msgid "Experience the fusion of imagination and expertise with Études—the catalyst for architectural transformations that enrich the world around us."
msgstr "Trải nghiệm sự kết hợp giữa trí tưởng tượng và chuyên môn với Études—chất xúc tác cho những thay đổi kiến trúc làm phong phú thêm thế giới xung quanh chúng ta."

#: patterns/cta-content-image-on-right.php:32
#: patterns/text-alternating-images.php:52
msgctxt "Sample list item"
msgid "Experience the world of architecture."
msgstr "Trải nghiệm thế giới kiến trúc."

#: patterns/cta-content-image-on-right.php:24
#: patterns/text-alternating-images.php:44
msgctxt "Sample list item"
msgid "Collaborate with fellow architects."
msgstr "Cộng tác với các kiến trúc sư đồng nghiệp."

#: patterns/cta-content-image-on-right.php:18
msgctxt "Sample heading"
msgid "Enhance your architectural journey with the Études Architect app."
msgstr "Nâng cao hành trình kiến trúc của bạn với ứng dụng Kiến trúc sư Études."

#: patterns/banner-project-description.php:17
msgctxt "Sample title for a project or post"
msgid "Art Gallery — Overview"
msgstr "Tổng quan về Phòng trưng bày Nghệ thuật"

#: patterns/banner-hero.php:26
msgctxt "Content of the hero section"
msgid "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."
msgstr "Études là một công ty tiên phong, kết hợp liền mạch giữa sáng tạo và chức năng để tái định nghĩa sự xuất sắc trong kiến trúc."

#: patterns/banner-hero.php
msgctxt "Pattern title"
msgid "Hero"
msgstr "Hero"

#: theme.json
msgctxt "Custom template name"
msgid "Single with Sidebar"
msgstr "Bài viết đơn với thanh bên"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Wide Image"
msgstr "Trang với hình ảnh rộng"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Trang không có tiêu đề"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Sidebar"
msgstr "Trang với thanh bên"

#: patterns/text-feature-grid-3-col.php:96
msgctxt "Sample feature heading"
msgid "Project Management"
msgstr "Quản lý dự án"

#: patterns/text-feature-grid-3-col.php:108
msgctxt "Sample heading"
msgid "Architectural Solutions"
msgstr "Giải pháp kiến trúc"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfour/"
msgstr "https://wordpress.org/themes/twentytwentyfour/"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "nhóm WordPress"
