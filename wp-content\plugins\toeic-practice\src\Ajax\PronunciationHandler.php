<?php
/**
 * Pronunciation Handler
 *
 * @package ToeicPractice\Ajax
 */

namespace ToeicPractice\Ajax;

use ToeicPractice\Plugin;
use ToeicPractice\Classes\AzurePronunciationService;

/**
 * Pronunciation Handler Class
 * 
 * Handles AJAX requests for pronunciation practice.
 */
class PronunciationHandler {
    
    /**
     * Register AJAX handlers
     */
    public function register() {
        // Pronunciation grading - available for both logged in and non-logged in users
        add_action('wp_ajax_grade_pronunciation', [$this, 'handlePronunciationGrading']);
        add_action('wp_ajax_nopriv_grade_pronunciation', [$this, 'handlePronunciationGrading']);
        add_action('wp_ajax_get_pronunciation_suggestions', [$this, 'handleGetSuggestions']);
    }
    
    /**
     * Handle pronunciation grading request
     */
    public function handlePronunciationGrading() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'toeic_practice_nonce')) {
                wp_send_json_error(__('Security check failed.', 'toeic-practice'));
            }

            $db = Plugin::getInstance()->getDatabase();
            
            // Validate required parameters
            $topic_id = intval($_POST['topic_id'] ?? 0);
            $item_id = intval($_POST['item_id'] ?? 0);
            $item_type = sanitize_text_field($_POST['item_type'] ?? 'question');
            
            if (!$topic_id || !$item_id) {
                wp_send_json_error(__('Missing required parameters.', 'toeic-practice'));
            }
            
            if (!in_array($item_type, ['question', 'vocabulary'])) {
                wp_send_json_error(__('Invalid item type.', 'toeic-practice'));
            }
            
            // Check if audio file was uploaded
            if (!isset($_FILES['audio']) || $_FILES['audio']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(__('No audio file received or upload error.', 'toeic-practice'));
            }
            
            // Get the practice item details
            $item_data = $this->getPracticeItem($item_id, $item_type);
            if (!$item_data) {
                wp_send_json_error(__('Practice item not found.', 'toeic-practice'));
            }
            
            // Verify the item belongs to the topic
            if (!$this->verifyItemInTopic($topic_id, $item_id, $item_type)) {
                wp_send_json_error(__('Item does not belong to the specified topic.', 'toeic-practice'));
            }
            
            // Process the audio file
            $audio_result = $this->processAudioFile($_FILES['audio']);
            if (!$audio_result['success']) {
                wp_send_json_error($audio_result['message']);
            }
            
            // Get expected text for pronunciation analysis
            $expected_text = $this->getExpectedText($item_data, $item_type);
            
            // Analyze pronunciation using Azure Speech Service or mock implementation
            $analysis_result = $this->analyzePronunciation($audio_result['file_path'], $expected_text);
            
            // Clean up temporary audio file
            if (file_exists($audio_result['file_path'])) {
                unlink($audio_result['file_path']);
            }
            
            wp_send_json_success([
                'message' => __('Pronunciation analyzed successfully.', 'toeic-practice'),
                'item_data' => $item_data,
                'analysis' => $analysis_result,
                'expected_text' => $expected_text
            ]);
            
        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Get practice item data
     *
     * @param int $item_id Item ID
     * @param string $item_type Item type (question|vocabulary)
     * @return array|null Item data or null if not found
     */
    private function getPracticeItem($item_id, $item_type) {
        $db = Plugin::getInstance()->getDatabase();
        
        if ($item_type === 'question') {
            return $db->getQuestion($item_id);
        } elseif ($item_type === 'vocabulary') {
            return $db->getVocabulary($item_id);
        }
        
        return null;
    }
    
    /**
     * Verify item belongs to topic
     *
     * @param int $topic_id Topic ID
     * @param int $item_id Item ID
     * @param string $item_type Item type
     * @return bool True if item belongs to topic
     */
    private function verifyItemInTopic($topic_id, $item_id, $item_type) {
        $db = Plugin::getInstance()->getDatabase();
        $topic_items = $db->getPronunciationTopics()->getTopicItems($topic_id);
        
        foreach ($topic_items as $topic_item) {
            if ($topic_item['item_id'] == $item_id && $topic_item['item_type'] === $item_type) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Process uploaded audio file
     *
     * @param array $file_data Uploaded file data
     * @return array Processing result
     */
    private function processAudioFile($file_data) {
        // Validate file type
        $allowed_types = ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/ogg'];
        $file_type = $file_data['type'];
        
        if (!in_array($file_type, $allowed_types)) {
            return [
                'success' => false,
                'message' => __('Invalid audio file type. Allowed types: WebM, WAV, MP3, OGG.', 'toeic-practice')
            ];
        }
        
        // Validate file size (max 10MB)
        $max_size = 10 * 1024 * 1024; // 10MB
        if ($file_data['size'] > $max_size) {
            return [
                'success' => false,
                'message' => __('Audio file too large. Maximum size: 10MB.', 'toeic-practice')
            ];
        }
        
        // Create temporary file
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/toeic-temp/';
        
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }
        
        $temp_filename = 'pronunciation_' . uniqid() . '_' . time() . $this->getAudioFileExtension($file_data['type']);
        $temp_filepath = $temp_dir . $temp_filename;
        
        // Move uploaded file to temp location
        if (!move_uploaded_file($file_data['tmp_name'], $temp_filepath)) {
            return [
                'success' => false,
                'message' => __('Failed to save audio file.', 'toeic-practice')
            ];
        }
        
        return [
            'success' => true,
            'file_path' => $temp_filepath,
            'file_name' => $temp_filename
        ];
    }
    
    /**
     * Get audio file extension based on MIME type
     *
     * @param string $mimeType MIME type of the audio file
     * @return string File extension
     */
    private function getAudioFileExtension($mimeType) {
        switch ($mimeType) {
            case 'audio/webm':
                return '.webm';
            case 'audio/wav':
                return '.wav';
            case 'audio/mp3':
                return '.mp3';
            case 'audio/ogg':
                return '.ogg';
            default:
                return '.webm';
        }
    }
    
    /**
     * Get expected text for pronunciation
     *
     * @param array $item_data Item data
     * @param string $item_type Item type
     * @return string Expected text
     */
    private function getExpectedText($item_data, $item_type) {
        if ($item_type === 'question') {
            // For questions, use the title or extract text from content
            return $item_data['title'] ?? '';
        } elseif ($item_type === 'vocabulary') {
            // For vocabulary, use the word
            return $item_data['word'] ?? '';
        }
        
        return '';
    }
    
    /**
     * Analyze pronunciation using Azure Speech Service
     * Falls back to mock implementation if Azure is not configured.
     *
     * @param string $audio_file_path Path to audio file
     * @param string $expected_text Expected text
     * @return array Analysis result
     */
    private function analyzePronunciation($audio_file_path, $expected_text) {
        // Try Azure pronunciation assessment first
        $azure_service = new AzurePronunciationService();

        if ($azure_service->isConfigured()) {
            $result = $azure_service->assessPronunciation($audio_file_path, $expected_text);
            
            if ($result !== false) {
                // Azure assessment successful
                return $result;
            } else {
                // Azure failed, log error and fall back to mock
                error_log('Azure pronunciation assessment failed, falling back to mock implementation');
            }
        } else {
            // Azure not configured, use mock implementation
            error_log('Azure pronunciation service not configured, using mock implementation');
        }
        
        // Fallback to mock implementation
        return $this->returnNotConfigured();
    }

    private function returnNotConfigured() {
        return [
            'overall_score' => 0,
            'accuracy' => 0,
            'fluency' => 0,
            'completeness' => 0,
            'feedback' => __('Azure pronunciation service not configured.', 'toeic-practice')
        ];
    }

    public function handleGetSuggestions() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'toeic_practice_nonce')) {
                wp_send_json_error(__('Security check failed.', 'toeic-practice'));
            }

            // Get suggestion from POST data
            $prompt = $_POST['prompt'];
            $subject = $_POST['subject'];

            // Format prompt
            $final_prompt = $this->makePronunciationPrompt($prompt, $subject);

            // TODO: Send prompt to OpenAI

            // TODO: Send OpenAI Response
            $suggestion = ""; // 

            wp_send_json_success([
                "test_data" => [
                    "prompt" => $prompt,
                    "subject" => $subject
                ],
                'message' => __('Suggestions generated successfully.', 'toeic-practice'),
                'suggestion_result' => $suggestion
            ]);
        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    private function makePronunciationPrompt($prompt, $subject) {
        $system_message = "
        You are an English teacher. 
        Your job is to help students improve their pronunciation. 
        You will be given a prompt and a subject. 
        The prompt will be a question or a statement. 
        The subject will be the text that the student is trying to pronounce. 
        Your job is to answer the prompt with respect to the subject. 
        Your answer should be in Vietnamese. 
        Your answer should be concise and to the point. Do not ask the user again, Only answer the prompt.
        ";

        $user_message = "
        
        ";

        return [
            "system_message" => $system_message,
            "user_message" => $user_message
        ];
    }
}
