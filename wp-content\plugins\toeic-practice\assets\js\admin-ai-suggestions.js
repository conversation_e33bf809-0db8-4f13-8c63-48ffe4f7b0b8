/**
 * Admin AI Suggestions JavaScript
 * 
 * Handles admin functionality for AI suggestions management
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize
    init();
    
    function init() {
        initSortable();
        initAddSuggestion();
        initRemoveSuggestion();
        initFormValidation();
    }
    
    /**
     * Initialize sortable functionality for suggestions
     */
    function initSortable() {
        if ($('#suggestions-list').length) {
            $('#suggestions-list').sortable({
                handle: '.handle',
                placeholder: 'ui-state-highlight',
                tolerance: 'pointer',
                cursor: 'move',
                opacity: 0.8,
                update: function(event, ui) {
                    updateSuggestionIndexes();
                    updateSuggestionNumbers();
                }
            });
        }
    }
    
    /**
     * Initialize add suggestion functionality
     */
    function initAddSuggestion() {
        $('#add-suggestion').on('click', function() {
            addNewSuggestion();
        });
    }
    
    /**
     * Initialize remove suggestion functionality
     */
    function initRemoveSuggestion() {
        $(document).on('click', '.remove-suggestion', function() {
            var suggestionItem = $(this).closest('.suggestion-item');
            var suggestionText = suggestionItem.find('.suggestion-text').val().trim();
            
            // Confirm deletion if there's content
            if (suggestionText.length > 0) {
                if (!confirm(toeicPracticeAdmin.strings.confirm_delete || 'Are you sure you want to remove this suggestion?')) {
                    return;
                }
            }
            
            removeSuggestion(suggestionItem);
        });
    }
    
    /**
     * Initialize form validation
     */
    function initFormValidation() {
        $('#ai-suggestions-form').on('submit', function(e) {
            var hasContent = false;
            
            // Check if at least one suggestion has content
            $('.suggestion-text').each(function() {
                if ($(this).val().trim().length > 0) {
                    hasContent = true;
                    return false; // Break the loop
                }
            });
            
            if (!hasContent) {
                alert(toeicPracticeAdmin.strings.no_suggestions || 'Please add at least one suggestion before saving.');
                e.preventDefault();
                return false;
            }
        });
    }
    
    /**
     * Add a new suggestion item
     */
    function addNewSuggestion() {
        var suggestionsList = $('#suggestions-list');
        var currentCount = suggestionsList.find('.suggestion-item').length;
        var newIndex = currentCount;
        
        var suggestionHtml = createSuggestionHtml(newIndex, '');
        suggestionsList.append(suggestionHtml);
        
        // Focus on the new textarea
        var newTextarea = suggestionsList.find('.suggestion-item:last .suggestion-text');
        newTextarea.focus();
        
        // Update indexes and numbers
        updateSuggestionIndexes();
        updateSuggestionNumbers();
        
        // Animate the new item
        var newItem = suggestionsList.find('.suggestion-item:last');
        newItem.hide().fadeIn(300);
    }
    
    /**
     * Remove a suggestion item
     */
    function removeSuggestion(suggestionItem) {
        suggestionItem.fadeOut(300, function() {
            $(this).remove();
            updateSuggestionIndexes();
            updateSuggestionNumbers();
            
            // If no suggestions left, add one empty suggestion
            if ($('#suggestions-list .suggestion-item').length === 0) {
                addNewSuggestion();
            }
        });
    }
    
    /**
     * Update suggestion indexes after sorting or removal
     */
    function updateSuggestionIndexes() {
        $('#suggestions-list .suggestion-item').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('textarea[name*="[text]"]').attr('name', 'suggestions[' + index + '][text]');
        });
    }
    
    /**
     * Update suggestion numbers display
     */
    function updateSuggestionNumbers() {
        $('#suggestions-list .suggestion-item').each(function(index) {
            $(this).find('.suggestion-number').text(index + 1);
        });
    }
    
    /**
     * Create HTML for a new suggestion item
     */
    function createSuggestionHtml(index, text) {
        return `
            <div class="suggestion-item" data-index="${index}">
                <div class="suggestion-header">
                    <span class="dashicons dashicons-menu handle" title="${escapeHtml(toeicPracticeAdmin.strings.drag_to_reorder || 'Drag to reorder')}"></span>
                    <span class="suggestion-number">${index + 1}</span>
                    <button type="button" class="remove-suggestion button-link-delete" title="${escapeHtml(toeicPracticeAdmin.strings.remove_suggestion || 'Remove suggestion')}">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
                <div class="suggestion-content">
                    <textarea 
                        name="suggestions[${index}][text]" 
                        class="suggestion-text large-text" 
                        rows="3" 
                        placeholder="${escapeHtml(toeicPracticeAdmin.strings.suggestion_placeholder || 'Enter your AI suggestion text here...')}"
                    >${escapeHtml(text)}</textarea>
                </div>
            </div>
        `;
    }
    
    /**
     * Escape HTML characters
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    
    /**
     * Auto-resize textareas
     */
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    }
    
    // Auto-resize textareas on input
    $(document).on('input', '.suggestion-text', function() {
        autoResizeTextarea(this);
    });
    
    // Auto-resize existing textareas on page load
    $('.suggestion-text').each(function() {
        autoResizeTextarea(this);
    });
    
    /**
     * Keyboard shortcuts
     */
    $(document).on('keydown', function(e) {
        // Ctrl/Cmd + Enter to save form
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
            $('#ai-suggestions-form').submit();
        }
        
        // Ctrl/Cmd + Plus to add new suggestion
        if ((e.ctrlKey || e.metaKey) && (e.keyCode === 107 || e.keyCode === 187)) {
            e.preventDefault();
            addNewSuggestion();
        }
    });
    
    /**
     * Handle textarea focus and blur for better UX
     */
    $(document).on('focus', '.suggestion-text', function() {
        $(this).closest('.suggestion-item').addClass('focused');
    });
    
    $(document).on('blur', '.suggestion-text', function() {
        $(this).closest('.suggestion-item').removeClass('focused');
    });
    
    /**
     * Show helpful tooltips
     */
    if (typeof $.fn.tooltip !== 'undefined') {
        $(document).on('mouseenter', '[title]', function() {
            $(this).tooltip({
                position: { my: "center bottom-20", at: "center top" }
            });
        });
    }
    
    /**
     * Prevent form submission on Enter in textareas (allow Shift+Enter for new lines)
     */
    $(document).on('keydown', '.suggestion-text', function(e) {
        if (e.keyCode === 13 && !e.shiftKey) {
            e.preventDefault();
            // Move to next textarea or add new one if this is the last
            var currentItem = $(this).closest('.suggestion-item');
            var nextItem = currentItem.next('.suggestion-item');
            
            if (nextItem.length) {
                nextItem.find('.suggestion-text').focus();
            } else {
                addNewSuggestion();
            }
        }
    });
});
