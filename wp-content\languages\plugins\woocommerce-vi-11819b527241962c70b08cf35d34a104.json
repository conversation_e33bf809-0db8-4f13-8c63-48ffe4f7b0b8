{"translation-revision-date": "2025-06-16 01:31:31+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "Classic Cart": ["Giỏ hàng cổ điển"], "Classic Checkout": ["<PERSON><PERSON> <PERSON><PERSON> cổ điển"], "Checkout Cart": ["Giỏ hàng thanh toán"], "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["Bạn có thể tìm hiểu thêm về lợi ích của việ<PERSON> chuyển sang khối, khả năng tương thích với tiện ích mở rộng và cách chuyển về mã rút gọn <a>trong tài liệu của chúng tôi</a>."], "Classic Shortcode Placeholder": ["Trình giữ chỗ mã rút gọn cũ"], "Classic shortcode transformed to blocks.": ["<PERSON><PERSON> rút gọn cũ được chuyển đổi thành khối."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["Khối này sẽ kết xuất mã rút gọn giỏ hàng cũ. Bạn có thể tùy chọn chuyển đổi nó thành khối để kiểm soát nhiều hơn đối với trải nghiệm giỏ hàng."], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["Khối này sẽ kết xuất mã rút gọn thanh toán cũ. Bạn có thể tùy chọn chuyển đổi nó thành khối để kiểm soát nhiều hơn đối với trải nghiệm thanh toán."], "Renders the classic checkout shortcode.": ["<PERSON><PERSON><PERSON> xuất mã rút gọn thanh toán cũ."], "Renders the classic cart shortcode.": ["<PERSON><PERSON><PERSON> xuất mã rút gọn giỏ hàng cũ."], "Cart Shortcode": ["<PERSON>ã rút gọn giỏ hàng"], "Transform into blocks": ["<PERSON>y<PERSON>n đổi thành các khối"], "Undo": ["<PERSON><PERSON><PERSON>"], "Learn more": ["<PERSON><PERSON><PERSON> hi<PERSON>u thêm"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}