<?php
/**
 * Vocabulary Edit Page
 *
 * @package ToeicPractice\Admin\Pages
 */

namespace ToeicPractice\Admin\Pages;

use ToeicPractice\Plugin;

/**
 * Vocabulary Edit Page Class
 * 
 * Handles editing vocabulary items in admin area
 */
class VocabularyEditPage {
    
    /**
     * Vocabulary item data
     *
     * @var array
     */
    private $vocabulary = [];
    
    /**
     * Display the edit vocabulary page
     */
    public function display() {
        // Get vocabulary ID
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            wp_die(__('Invalid vocabulary ID', 'toeic-practice'));
        }
        
        // Get database manager
        $db = Plugin::getInstance()->getDatabase();
        
        // Get vocabulary item
        $this->vocabulary = $db->getVocabulary($id);
        
        if (!$this->vocabulary) {
            wp_die(__('Vocabulary item not found', 'toeic-practice'));
        }
        
        // Process form submission
        if (isset($_POST['submit_vocabulary'])) {
            $this->processFormSubmission($id, $db);
        }
        
        $this->renderForm();
    }
    
    /**
     * Process form submission
     *
     * @param int $id Vocabulary ID
     * @param \ToeicPractice\Database\DatabaseManager $db Database manager
     */
    private function processFormSubmission($id, $db) {
        // Verify nonce
        if (!isset($_POST['toeic_vocabulary_nonce']) || !wp_verify_nonce($_POST['toeic_vocabulary_nonce'], 'edit_vocabulary_' . $id)) {
            wp_die(__('Security check failed', 'toeic-practice'));
        }
        
        // Get form data
        $word = isset($_POST['word']) ? sanitize_text_field($_POST['word']) : '';
        $vi_translate = isset($_POST['vi_translate']) ? sanitize_textarea_field($_POST['vi_translate']) : '';
        $audio_link = isset($_POST['audio_link']) ? esc_url_raw($_POST['audio_link']) : '';
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
        $example_sentence = isset($_POST['example_sentence']) ? sanitize_textarea_field($_POST['example_sentence']) : '';
        
        // Validate required fields
        $errors = [];
        
        if (empty($word)) {
            $errors[] = __('Word is required.', 'toeic-practice');
        }
        
        if (empty($vi_translate)) {
            $errors[] = __('Vietnamese translation is required.', 'toeic-practice');
        }
        
        // If there are errors, display them and return
        if (!empty($errors)) {
            foreach ($errors as $error) {
                add_settings_error(
                    'toeic_vocabulary',
                    'toeic_vocabulary_error',
                    $error,
                    'error'
                );
            }
            return;
        }
        
        // Handle audio file upload if provided
        $audio_file_url = $audio_link;
        
        // Check if the audio should be removed
        $remove_audio = isset($_POST['remove_audio']) && $_POST['remove_audio'] === '1';

        if ($remove_audio) {
            // Set audio link to empty to remove it
            $audio_file_url = '';
        } elseif (!empty($_FILES['audio_file']['name'])) {
            // Upload new audio file
            $audio_file_url = $this->uploadAudioFile();
            
            if (is_wp_error($audio_file_url)) {
                add_settings_error(
                    'toeic_vocabulary',
                    'toeic_vocabulary_error',
                    $audio_file_url->get_error_message(),
                    'error'
                );
                return;
            }
        }
        
        // Update vocabulary data
        $data = [
            'word' => $word,
            'vi_translate' => $vi_translate,
            'category_id' => $category_id,
            'example_sentence' => $example_sentence,
        ];
        
        // Only update audio link if a new one is provided
        $data['audio_link'] = $audio_file_url;
        
        // Update vocabulary
        $result = $db->updateVocabulary($id, $data);
        
        if ($result) {
            // Add success message
            add_settings_error(
                'toeic_vocabulary',
                'toeic_vocabulary_success',
                __('Vocabulary updated successfully.', 'toeic-practice'),
                'success'
            );
            
            // Refresh vocabulary data
            $this->vocabulary = $db->getVocabulary($id);
        } else {
            // Add error message
            add_settings_error(
                'toeic_vocabulary',
                'toeic_vocabulary_error',
                __('Failed to update vocabulary.', 'toeic-practice'),
                'error'
            );
        }
    }
    
    /**
     * Upload audio file
     * 
     * @return string|WP_Error URL of uploaded file or WP_Error on failure
     */
    private function uploadAudioFile() {
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        $uploadedfile = $_FILES['audio_file'];
        
        $allowed_types = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
        $filetype = wp_check_filetype($uploadedfile['name']);
        
        if (!in_array($filetype['type'], $allowed_types)) {
            return new \WP_Error('invalid_filetype', __('Invalid file type. Only MP3, WAV, and OGG files are allowed.', 'toeic-practice'));
        }
        
        $upload_overrides = [
            'test_form' => false,
            'test_size' => true,
            'test_upload' => true,
        ];
        
        $movefile = wp_handle_upload($uploadedfile, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            return $movefile['url'];
        } else {
            return new \WP_Error('upload_error', $movefile['error']);
        }
    }
    
    /**
     * Render the edit vocabulary form
     */
    private function renderForm() {
        // Get any settings errors
        settings_errors('toeic_vocabulary');
        
        ?>
        <div class="wrap toeic-vocabulary-edit">
            <h1><?php echo esc_html__('Edit Vocabulary', 'toeic-practice'); ?></h1>
            
            <form method="post" enctype="multipart/form-data">
                <?php wp_nonce_field('edit_vocabulary_' . $this->vocabulary['id'], 'toeic_vocabulary_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="word"><?php echo esc_html__('Word', 'toeic-practice'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <input type="text" name="word" id="word" class="regular-text" value="<?php echo esc_attr($this->vocabulary['word']); ?>" required>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="vi_translate"><?php echo esc_html__('Vietnamese Translation', 'toeic-practice'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <textarea name="vi_translate" id="vi_translate" class="large-text" rows="3" required><?php echo esc_textarea($this->vocabulary['vi_translate']); ?></textarea>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="audio_file"><?php echo esc_html__('Audio File', 'toeic-practice'); ?></label>
                        </th>
                        <td>
                            <input type="file" name="audio_file" id="audio_file" accept="audio/mp3,audio/mpeg,audio/wav,audio/ogg">
                            <p class="description"><?php echo esc_html__('Upload a new audio file. Leave empty to keep the current one.', 'toeic-practice'); ?></p>
                            
                            <input type="hidden" name="remove_audio" id="remove_audio" value="0">
                            <?php if (!empty($this->vocabulary['audio_link'])) : ?>
                                <div class="current-audio" style="margin-top: 10px;">
                                    <h4><?php echo esc_html__('Current Audio', 'toeic-practice'); ?></h4>
                                    <div class="audio-container">
                                        <audio id="vocabulary-audio" controls>
                                            <source src="<?php echo esc_url($this->vocabulary['audio_link']); ?>" type="audio/mpeg">
                                            <?php echo esc_html__('Your browser does not support the audio element.', 'toeic-practice'); ?>
                                        </audio>
                                        <div class="audio-buttons" style="margin-top: 10px;">
                                            <button type="button" id="remove-audio-btn" class="button button-secondary">
                                                <span class="dashicons dashicons-trash"></span> <?php echo esc_html__('Remove Current Audio', 'toeic-practice'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="audio_link"><?php echo esc_html__('Or Audio URL', 'toeic-practice'); ?></label>
                        </th>
                        <td>
                            <input type="url" name="audio_link" id="audio_link" class="regular-text" value="<?php echo esc_attr($this->vocabulary['audio_link']); ?>">
                            <p class="description"><?php echo esc_html__('Enter a URL to an audio file.', 'toeic-practice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="category_id"><?php echo esc_html__('Category', 'toeic-practice'); ?></label>
                        </th>
                        <td>
                            <select name="category_id" id="category_id">
                                <option value="0"><?php echo esc_html__('-- Select Category --', 'toeic-practice'); ?></option>
                                <?php
                                // Get categories from taxonomy if available
                                $categories = get_terms([
                                    'taxonomy' => 'vocabulary_category',
                                    'hide_empty' => false,
                                ]);
                                
                                if (!is_wp_error($categories) && !empty($categories)) {
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '" ' . selected($this->vocabulary['category_id'], $category->term_id, false) . '>' . esc_html($category->name) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="example_sentence"><?php echo esc_html__('Example Sentence', 'toeic-practice'); ?></label>
                        </th>
                        <td>
                            <textarea name="example_sentence" id="example_sentence" class="large-text" rows="3"><?php echo esc_textarea($this->vocabulary['example_sentence']); ?></textarea>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit_vocabulary" id="submit" class="button button-primary" value="<?php echo esc_attr__('Update Vocabulary', 'toeic-practice'); ?>">
                    <a href="<?php echo esc_url(admin_url('admin.php?page=toeic-practice-vocabulary')); ?>" class="button"><?php echo esc_html__('Cancel', 'toeic-practice'); ?></a>
                </p>
            </form>
        </div>
        <?php
    }
}
