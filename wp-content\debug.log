[31-Jul-2025 04:32:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:32:24 UTC] PHP Stack trace:
[31-Jul-2025 04:32:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:32:24 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:32:24 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:32:24 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:32:24 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:32:24 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:32:24 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:32:24 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:32:24 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:32:24 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:32:24 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:32:24 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:32:24 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:32:24 UTC] PHP Warning:  require_once(D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php): Failed to open stream: No such file or directory in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:32:24 UTC] PHP Stack trace:
[31-Jul-2025 04:32:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:32:24 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:32:24 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:32:24 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:32:24 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:32:24 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:32:24 UTC] PHP Fatal error:  Uncaught Error: Failed opening required 'D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php' (include_path='.;C:\php\pear') in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php:34
Stack trace:
#0 D:\wamp64\www\toeictest\wp-settings.php(545): include_once()
#1 D:\wamp64\www\toeictest\wp-config.php(104): require_once('D:\\wamp64\\www\\t...')
#2 D:\wamp64\www\toeictest\wp-load.php(50): require_once('D:\\wamp64\\www\\t...')
#3 D:\wamp64\www\toeictest\wp-blog-header.php(13): require_once('D:\\wamp64\\www\\t...')
#4 D:\wamp64\www\toeictest\index.php(17): require('D:\\wamp64\\www\\t...')
#5 {main}
  thrown in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:32:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:32:24 UTC] PHP Stack trace:
[31-Jul-2025 04:32:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:32:24 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:32:24 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:32:24 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:32:24 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:32:24 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:32:24 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:32:24 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:32:24 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:32:24 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:32:24 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:32:24 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:32:24 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:32:24 UTC] PHP Warning:  require_once(D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php): Failed to open stream: No such file or directory in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:32:24 UTC] PHP Stack trace:
[31-Jul-2025 04:32:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:32:24 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:32:24 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:32:24 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:32:24 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:32:24 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:32:24 UTC] PHP Fatal error:  Uncaught Error: Failed opening required 'D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php' (include_path='.;C:\php\pear') in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php:34
Stack trace:
#0 D:\wamp64\www\toeictest\wp-settings.php(545): include_once()
#1 D:\wamp64\www\toeictest\wp-config.php(104): require_once('D:\\wamp64\\www\\t...')
#2 D:\wamp64\www\toeictest\wp-load.php(50): require_once('D:\\wamp64\\www\\t...')
#3 D:\wamp64\www\toeictest\wp-blog-header.php(13): require_once('D:\\wamp64\\www\\t...')
#4 D:\wamp64\www\toeictest\index.php(17): require('D:\\wamp64\\www\\t...')
#5 {main}
  thrown in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:33:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:09 UTC] PHP Stack trace:
[31-Jul-2025 04:33:09 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:33:09 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:33:09 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:09 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:09 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:09 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:09 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:09 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:09 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:09 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:09 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:09 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:09 UTC] PHP Warning:  require_once(D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php): Failed to open stream: No such file or directory in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:33:09 UTC] PHP Stack trace:
[31-Jul-2025 04:33:09 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:33:09 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:33:09 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:09 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:09 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:09 UTC] PHP Fatal error:  Uncaught Error: Failed opening required 'D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice/vendor/autoload.php' (include_path='.;C:\php\pear') in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php:34
Stack trace:
#0 D:\wamp64\www\toeictest\wp-settings.php(545): include_once()
#1 D:\wamp64\www\toeictest\wp-config.php(104): require_once('D:\\wamp64\\www\\t...')
#2 D:\wamp64\www\toeictest\wp-load.php(50): require_once('D:\\wamp64\\www\\t...')
#3 D:\wamp64\www\toeictest\wp-admin\admin-ajax.php(22): require_once('D:\\wamp64\\www\\t...')
#4 {main}
  thrown in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\toeic-practice.php on line 34
[31-Jul-2025 04:33:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:37 UTC] PHP Stack trace:
[31-Jul-2025 04:33:37 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:33:37 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:33:37 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:33:37 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:37 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:37 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:37 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:37 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:37 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:37 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:37 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:37 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:37 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:38 UTC] PHP Stack trace:
[31-Jul-2025 04:33:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:33:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:33:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:38 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:38 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:38 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:38 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:38 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:38 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:38 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:38 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:38 UTC] PHP Stack trace:
[31-Jul-2025 04:33:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:33:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:33:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:38 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:38 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:38 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:38 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:38 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:38 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:38 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:38 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:39 UTC] PHP Stack trace:
[31-Jul-2025 04:33:39 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:33:39 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:33:39 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:33:39 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:39 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:39 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:39 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:39 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:39 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:39 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:39 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:39 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:39 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:39 UTC] PHP Stack trace:
[31-Jul-2025 04:33:39 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:33:39 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:33:39 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:39 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:39 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:39 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:39 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:39 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:39 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:39 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:39 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:39 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:33:39 UTC] PHP Stack trace:
[31-Jul-2025 04:33:39 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:33:39 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:33:39 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:33:39 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:33:39 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:33:39 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:33:39 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:33:39 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:33:39 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:33:39 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:33:39 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:33:39 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:33:40 UTC] PHP Warning:  Undefined array key "content" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:33:40 UTC] PHP Stack trace:
[31-Jul-2025 04:33:40 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:33:40 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:33:40 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:33:40 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:33:40 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:34:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:34:29 UTC] PHP Stack trace:
[31-Jul-2025 04:34:29 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:34:29 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:34:29 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:34:29 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:34:29 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:34:29 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:34:29 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:34:29 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:34:29 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:34:29 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:34:29 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:34:29 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:34:29 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:34:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:34:30 UTC] PHP Stack trace:
[31-Jul-2025 04:34:30 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:34:30 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:34:30 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:34:30 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:34:30 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:34:30 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:34:30 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:34:30 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:34:30 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:34:30 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:34:30 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:34:30 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:34:30 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:34:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:34:31 UTC] PHP Stack trace:
[31-Jul-2025 04:34:31 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:34:31 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:34:31 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:34:31 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:34:31 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:34:31 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:34:31 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:34:31 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:34:31 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:34:31 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:34:31 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:34:31 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:34:31 UTC] PHP Warning:  Undefined array key "content" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:34:31 UTC] PHP Stack trace:
[31-Jul-2025 04:34:31 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:34:31 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:34:31 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:34:31 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:34:31 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:35:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:35:10 UTC] PHP Stack trace:
[31-Jul-2025 04:35:10 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:35:10 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:35:10 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:35:10 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:35:10 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:35:10 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:35:10 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:35:10 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:35:10 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:35:10 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:35:10 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:35:10 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:35:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:35:11 UTC] PHP Stack trace:
[31-Jul-2025 04:35:11 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:35:11 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:35:11 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:35:11 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:35:11 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:35:11 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:35:11 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:35:11 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:35:11 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:35:11 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:35:11 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:35:11 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:35:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:35:22 UTC] PHP Stack trace:
[31-Jul-2025 04:35:22 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:35:22 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:35:22 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:35:22 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:35:22 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:35:22 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:35:22 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:35:22 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:35:22 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:35:22 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:35:22 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:35:22 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:35:22 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:35:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:35:23 UTC] PHP Stack trace:
[31-Jul-2025 04:35:23 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:35:23 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:35:23 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:35:23 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:35:23 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:35:23 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:35:23 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:35:23 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:35:23 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:35:23 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:35:23 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:35:23 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:35:23 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:35:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:35:24 UTC] PHP Stack trace:
[31-Jul-2025 04:35:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:35:24 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:35:24 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:35:24 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:35:24 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:35:24 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:35:24 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:35:24 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:35:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:35:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:35:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:35:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:35:24 UTC] PHP Warning:  Undefined array key "content" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:35:24 UTC] PHP Stack trace:
[31-Jul-2025 04:35:24 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:35:24 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:35:24 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:35:24 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:35:24 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:36:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:27 UTC] PHP Stack trace:
[31-Jul-2025 04:36:27 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:36:27 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:36:27 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:36:27 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:27 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:27 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:27 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:27 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:27 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:27 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:27 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:27 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:27 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:27 UTC] PHP Stack trace:
[31-Jul-2025 04:36:27 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:36:27 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:36:27 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:27 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:27 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:27 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:27 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:27 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:27 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:27 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:27 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:27 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:28 UTC] PHP Stack trace:
[31-Jul-2025 04:36:28 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:36:28 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:36:28 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:36:28 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:28 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:28 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:28 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:28 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:28 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:28 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:28 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:28 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:28 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:29 UTC] PHP Stack trace:
[31-Jul-2025 04:36:29 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:36:29 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:36:29 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:29 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:29 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:29 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:29 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:29 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:29 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:29 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:29 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:29 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:29 UTC] PHP Warning:  Undefined array key "content" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:36:29 UTC] PHP Stack trace:
[31-Jul-2025 04:36:29 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:36:29 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:36:29 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:36:29 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:36:29 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:36:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:41 UTC] PHP Stack trace:
[31-Jul-2025 04:36:41 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:36:41 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:36:41 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:36:41 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:41 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:41 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:41 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:41 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:41 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:41 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:41 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:41 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:41 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:41 UTC] PHP Stack trace:
[31-Jul-2025 04:36:41 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[31-Jul-2025 04:36:41 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[31-Jul-2025 04:36:41 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[31-Jul-2025 04:36:41 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:41 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:41 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:41 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:41 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:41 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:41 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:41 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:41 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:41 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:36:43 UTC] PHP Stack trace:
[31-Jul-2025 04:36:43 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:36:43 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:36:43 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:36:43 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:36:43 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:36:43 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:36:43 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:36:43 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:36:43 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:36:43 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:36:43 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:36:43 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:36:44 UTC] PHP Warning:  Undefined array key "content" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:36:44 UTC] PHP Stack trace:
[31-Jul-2025 04:36:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:36:44 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:36:44 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:36:44 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:36:44 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:37:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:37:11 UTC] PHP Stack trace:
[31-Jul-2025 04:37:11 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:37:11 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:37:11 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:37:11 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:37:11 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:37:11 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:37:11 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:37:11 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:37:11 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:37:11 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:37:11 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:37:11 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:37:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:37:12 UTC] PHP Stack trace:
[31-Jul-2025 04:37:12 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:37:12 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:37:12 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:37:12 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:37:12 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:37:12 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:37:12 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:37:12 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:37:12 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:37:12 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:37:12 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:37:12 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:38:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:38:31 UTC] PHP Stack trace:
[31-Jul-2025 04:38:31 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:38:31 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:38:31 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:38:31 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:38:31 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:38:31 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:38:31 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:38:31 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:38:31 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:38:31 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:38:31 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:38:31 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:38:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:38:32 UTC] PHP Stack trace:
[31-Jul-2025 04:38:32 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:38:32 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:38:32 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:38:32 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:38:32 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:38:32 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:38:32 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:38:32 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:38:32 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:38:32 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:38:32 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:38:32 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:38:32 UTC] PHP Warning:  Undefined array key "promt" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 282
[31-Jul-2025 04:38:32 UTC] PHP Stack trace:
[31-Jul-2025 04:38:32 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:38:32 UTC] PHP   2. do_action($hook_name = 'wp_ajax_get_pronunciation_suggestions') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[31-Jul-2025 04:38:32 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[31-Jul-2025 04:38:32 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[31-Jul-2025 04:38:32 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handleGetSuggestions('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[31-Jul-2025 04:38:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:38:53 UTC] PHP Stack trace:
[31-Jul-2025 04:38:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:38:53 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:38:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:38:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:38:53 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:38:53 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:38:53 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:38:53 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:38:53 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:38:53 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:38:53 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:38:53 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:39:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:39:12 UTC] PHP Stack trace:
[31-Jul-2025 04:39:12 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:39:12 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:39:12 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:39:12 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:39:12 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:39:12 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:39:12 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:39:12 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:39:12 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:39:12 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:39:12 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:39:12 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:39:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:39:13 UTC] PHP Stack trace:
[31-Jul-2025 04:39:13 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:39:13 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:39:13 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:39:13 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:39:13 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:39:13 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:39:13 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:39:13 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:39:13 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:39:13 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:39:13 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:39:13 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:41:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:41:13 UTC] PHP Stack trace:
[31-Jul-2025 04:41:13 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:41:13 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:41:13 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:41:13 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:41:13 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:41:13 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:41:13 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:41:13 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:41:13 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:41:13 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:41:13 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:41:13 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:41:14 UTC] PHP Parse error:  syntax error, unexpected token "return" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 310
[31-Jul-2025 04:41:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:41:14 UTC] PHP Stack trace:
[31-Jul-2025 04:41:14 UTC] PHP   1. WooCommerce->log_errors() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:0
[31-Jul-2025 04:41:14 UTC] PHP   2. WC_Logger->critical($message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:422
[31-Jul-2025 04:41:14 UTC] PHP   3. WC_Logger->log($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:236
[31-Jul-2025 04:41:14 UTC] PHP   4. Automattic\WooCommerce\Internal\Logging\RemoteLogger->handle($timestamp = 1753936874, $level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:189
[31-Jul-2025 04:41:14 UTC] PHP   5. Automattic\WooCommerce\Internal\Logging\RemoteLogger->should_handle($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:46
[31-Jul-2025 04:41:14 UTC] PHP   6. Automattic\WooCommerce\Internal\Logging\RemoteLogger->is_remote_logging_allowed() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:177
[31-Jul-2025 04:41:14 UTC] PHP   7. Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:147
[31-Jul-2025 04:41:14 UTC] PHP   8. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Utilities\FeaturesUtil.php:42
[31-Jul-2025 04:41:14 UTC] PHP   9. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_exists($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:593
[31-Jul-2025 04:41:14 UTC] PHP  10. Automattic\WooCommerce\Internal\Features\FeaturesController->get_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:697
[31-Jul-2025 04:41:14 UTC] PHP  11. Automattic\WooCommerce\Internal\Features\FeaturesController->init_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:214
[31-Jul-2025 04:41:14 UTC] PHP  12. __($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:248
[31-Jul-2025 04:41:14 UTC] PHP  13. translate($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:41:14 UTC] PHP  14. get_translations_for_domain($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:41:14 UTC] PHP  15. _load_textdomain_just_in_time($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:41:14 UTC] PHP  16. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:41:14 UTC] PHP  17. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:41:14 UTC] PHP  18. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:43:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:43:14 UTC] PHP Stack trace:
[31-Jul-2025 04:43:14 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:43:14 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:43:14 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:43:14 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:43:14 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:43:14 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:43:14 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:43:14 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:43:14 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:43:14 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:43:14 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:43:14 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:43:15 UTC] PHP Parse error:  syntax error, unexpected token "return" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 310
[31-Jul-2025 04:43:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:43:15 UTC] PHP Stack trace:
[31-Jul-2025 04:43:15 UTC] PHP   1. WooCommerce->log_errors() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:0
[31-Jul-2025 04:43:15 UTC] PHP   2. WC_Logger->critical($message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:422
[31-Jul-2025 04:43:15 UTC] PHP   3. WC_Logger->log($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:236
[31-Jul-2025 04:43:15 UTC] PHP   4. Automattic\WooCommerce\Internal\Logging\RemoteLogger->handle($timestamp = 1753936995, $level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:189
[31-Jul-2025 04:43:15 UTC] PHP   5. Automattic\WooCommerce\Internal\Logging\RemoteLogger->should_handle($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:46
[31-Jul-2025 04:43:15 UTC] PHP   6. Automattic\WooCommerce\Internal\Logging\RemoteLogger->is_remote_logging_allowed() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:177
[31-Jul-2025 04:43:15 UTC] PHP   7. Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:147
[31-Jul-2025 04:43:15 UTC] PHP   8. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Utilities\FeaturesUtil.php:42
[31-Jul-2025 04:43:15 UTC] PHP   9. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_exists($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:593
[31-Jul-2025 04:43:15 UTC] PHP  10. Automattic\WooCommerce\Internal\Features\FeaturesController->get_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:697
[31-Jul-2025 04:43:15 UTC] PHP  11. Automattic\WooCommerce\Internal\Features\FeaturesController->init_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:214
[31-Jul-2025 04:43:15 UTC] PHP  12. __($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:248
[31-Jul-2025 04:43:15 UTC] PHP  13. translate($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:43:15 UTC] PHP  14. get_translations_for_domain($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:43:15 UTC] PHP  15. _load_textdomain_just_in_time($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:43:15 UTC] PHP  16. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:43:15 UTC] PHP  17. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:43:15 UTC] PHP  18. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:45:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:45:15 UTC] PHP Stack trace:
[31-Jul-2025 04:45:15 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:45:15 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:45:15 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:45:15 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:45:15 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:45:15 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:45:15 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:45:15 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:45:15 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:45:15 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:45:15 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:45:15 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:45:16 UTC] PHP Parse error:  syntax error, unexpected token "return" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 310
[31-Jul-2025 04:45:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:45:16 UTC] PHP Stack trace:
[31-Jul-2025 04:45:16 UTC] PHP   1. WooCommerce->log_errors() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:0
[31-Jul-2025 04:45:16 UTC] PHP   2. WC_Logger->critical($message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:422
[31-Jul-2025 04:45:16 UTC] PHP   3. WC_Logger->log($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:236
[31-Jul-2025 04:45:16 UTC] PHP   4. Automattic\WooCommerce\Internal\Logging\RemoteLogger->handle($timestamp = 1753937116, $level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:189
[31-Jul-2025 04:45:16 UTC] PHP   5. Automattic\WooCommerce\Internal\Logging\RemoteLogger->should_handle($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:46
[31-Jul-2025 04:45:16 UTC] PHP   6. Automattic\WooCommerce\Internal\Logging\RemoteLogger->is_remote_logging_allowed() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:177
[31-Jul-2025 04:45:16 UTC] PHP   7. Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:147
[31-Jul-2025 04:45:16 UTC] PHP   8. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Utilities\FeaturesUtil.php:42
[31-Jul-2025 04:45:16 UTC] PHP   9. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_exists($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:593
[31-Jul-2025 04:45:16 UTC] PHP  10. Automattic\WooCommerce\Internal\Features\FeaturesController->get_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:697
[31-Jul-2025 04:45:16 UTC] PHP  11. Automattic\WooCommerce\Internal\Features\FeaturesController->init_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:214
[31-Jul-2025 04:45:16 UTC] PHP  12. __($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:248
[31-Jul-2025 04:45:16 UTC] PHP  13. translate($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:45:16 UTC] PHP  14. get_translations_for_domain($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:45:16 UTC] PHP  15. _load_textdomain_just_in_time($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:45:16 UTC] PHP  16. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:45:16 UTC] PHP  17. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:45:16 UTC] PHP  18. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:47:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:47:16 UTC] PHP Stack trace:
[31-Jul-2025 04:47:16 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:47:16 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:47:16 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:47:16 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:47:16 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:47:16 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:47:16 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:47:16 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:47:16 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:47:16 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:47:16 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:47:16 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:47:16 UTC] PHP Parse error:  syntax error, unexpected token "return" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 310
[31-Jul-2025 04:47:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:47:17 UTC] PHP Stack trace:
[31-Jul-2025 04:47:17 UTC] PHP   1. WooCommerce->log_errors() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:0
[31-Jul-2025 04:47:17 UTC] PHP   2. WC_Logger->critical($message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-woocommerce.php:422
[31-Jul-2025 04:47:17 UTC] PHP   3. WC_Logger->log($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:236
[31-Jul-2025 04:47:17 UTC] PHP   4. Automattic\WooCommerce\Internal\Logging\RemoteLogger->handle($timestamp = 1753937237, $level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\includes\class-wc-logger.php:189
[31-Jul-2025 04:47:17 UTC] PHP   5. Automattic\WooCommerce\Internal\Logging\RemoteLogger->should_handle($level = 'critical', $message = 'syntax error, unexpected token "return"', $context = ['source' => 'fatal-errors', 'error' => ['type' => 4, 'file' => 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\toeic-practice\\src\\Ajax\\PronunciationHandler.php', 'line' => 310], 'remote-logging' => TRUE, 'backtrace' => TRUE]) D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:46
[31-Jul-2025 04:47:17 UTC] PHP   6. Automattic\WooCommerce\Internal\Logging\RemoteLogger->is_remote_logging_allowed() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:177
[31-Jul-2025 04:47:17 UTC] PHP   7. Automattic\WooCommerce\Utilities\FeaturesUtil::feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Logging\RemoteLogger.php:147
[31-Jul-2025 04:47:17 UTC] PHP   8. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_is_enabled($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Utilities\FeaturesUtil.php:42
[31-Jul-2025 04:47:17 UTC] PHP   9. Automattic\WooCommerce\Internal\Features\FeaturesController->feature_exists($feature_id = 'remote_logging') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:593
[31-Jul-2025 04:47:17 UTC] PHP  10. Automattic\WooCommerce\Internal\Features\FeaturesController->get_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:697
[31-Jul-2025 04:47:17 UTC] PHP  11. Automattic\WooCommerce\Internal\Features\FeaturesController->init_feature_definitions() D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:214
[31-Jul-2025 04:47:17 UTC] PHP  12. __($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-content\plugins\woocommerce\src\Internal\Features\FeaturesController.php:248
[31-Jul-2025 04:47:17 UTC] PHP  13. translate($text = 'Analytics', $domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:47:17 UTC] PHP  14. get_translations_for_domain($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:47:17 UTC] PHP  15. _load_textdomain_just_in_time($domain = 'woocommerce') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:47:17 UTC] PHP  16. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:47:17 UTC] PHP  17. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:47:17 UTC] PHP  18. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:49:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:49:17 UTC] PHP Stack trace:
[31-Jul-2025 04:49:17 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:49:17 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:49:17 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:49:17 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:49:17 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:49:17 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:49:17 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:49:17 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:49:17 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:49:17 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:49:17 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:49:17 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:49:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:49:18 UTC] PHP Stack trace:
[31-Jul-2025 04:49:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:49:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:49:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:49:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:49:18 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:49:18 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:49:18 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:49:18 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:49:18 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:49:18 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:49:18 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:49:18 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:51:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:51:18 UTC] PHP Stack trace:
[31-Jul-2025 04:51:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:51:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:51:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:51:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:51:18 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:51:18 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:51:18 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:51:18 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:51:18 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:51:18 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:51:18 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:51:18 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:51:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:51:19 UTC] PHP Stack trace:
[31-Jul-2025 04:51:19 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:51:19 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:51:19 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:51:19 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:51:19 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:51:19 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:51:19 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:51:19 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:51:19 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:51:19 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:51:19 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:51:19 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:53:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:53:19 UTC] PHP Stack trace:
[31-Jul-2025 04:53:19 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:53:19 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:53:19 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:53:19 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:53:19 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:53:19 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:53:19 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:53:19 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:53:19 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:53:19 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:53:19 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:53:19 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:53:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:53:20 UTC] PHP Stack trace:
[31-Jul-2025 04:53:20 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:53:20 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:53:20 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:53:20 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:53:20 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:53:20 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:53:20 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:53:20 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:53:20 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:53:20 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:53:20 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:53:20 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:55:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:55:20 UTC] PHP Stack trace:
[31-Jul-2025 04:55:20 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[31-Jul-2025 04:55:20 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[31-Jul-2025 04:55:20 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:55:20 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:55:20 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:55:20 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:55:20 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:55:20 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:55:20 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:55:20 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:55:20 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:55:20 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[31-Jul-2025 04:55:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[31-Jul-2025 04:55:21 UTC] PHP Stack trace:
[31-Jul-2025 04:55:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[31-Jul-2025 04:55:21 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[31-Jul-2025 04:55:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[31-Jul-2025 04:55:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[31-Jul-2025 04:55:21 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[31-Jul-2025 04:55:21 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[31-Jul-2025 04:55:21 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[31-Jul-2025 04:55:21 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[31-Jul-2025 04:55:21 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[31-Jul-2025 04:55:21 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[31-Jul-2025 04:55:21 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[31-Jul-2025 04:55:21 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
