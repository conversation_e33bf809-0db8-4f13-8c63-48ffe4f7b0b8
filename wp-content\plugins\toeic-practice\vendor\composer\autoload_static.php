<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit7b6669fbc9d2aa7b1f27f35d3e1dc87b
{
    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInit7b6669fbc9d2aa7b1f27f35d3e1dc87b::$classMap;

        }, null, ClassLoader::class);
    }
}
