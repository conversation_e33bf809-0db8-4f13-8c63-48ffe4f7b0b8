<?php
/**
 * Pronunciation Practice page template
 *
 * @package ToeicPractice
 */

use \ToeicPractice\Classes\PronunciationItem;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$frontend = \ToeicPractice\Plugin::getInstance()->getFrontend();
$router = $frontend->getRouter();

// Get topic item data
$topic_id = $router->getDynamicVar('topic_id');
$item_id = $router->getDynamicVar('item_id');

// Get data
$item = new PronunciationItem($topic_id, $item_id);
$item_type = $item->getItemType();
$item_content = $item->getContent();

// Get suggestions
$ai_suggestions = get_option('toeic_practice_ai_suggestion_list');
?>

<?php include_once TOEIC_PRACTICE_PLUGIN_DIR . 'templates/parts/header.php'; ?>

<div class="toeic-container">
    <div class="toeic-content pronunciation-practice-content">
        <div class="practice-header">
            <h1><?php echo esc_html__('Pronunciation Practice', 'toeic-practice'); ?></h1>
            <div class="practice-nav">
                <!-- BreadCrumb -->
                <div class="breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb-list">
                            <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url()); ?>"><?php echo esc_html__('Home', 'toeic-practice'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/toeic/pronunciation-topics')); ?>"><?php echo esc_html__('Topics', 'toeic-practice'); ?></a></li>
                            <li class="breadcrumb-item active"><?php echo esc_html__('Practice', 'toeic-practice'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <div class="practice-layout">
            <!-- Left Panel: Recording & Rating (2/3 width) -->
            <div class="practice-main">
                <!-- Practice item -->
                <div class="practice-item">
                    <div class="practice-item-header">
                        <h3><?php echo esc_html__('Read the following content', 'toeic-practice'); ?></h3>
                    </div>
                    
                    <div class="practice-item-content">
                        <div class="practice-item-text">
                            <p>"<?php echo $item_content; ?>"</p>
                        </div>
                    </div>
                </div>

                <div class="practice-result"></div>
                
                <div class="recording-controls">
                    <div class="recording-status">
                        <span class="recording-text"><?php echo esc_html__('Recording in progress...', 'toeic-practice'); ?></span>
                    </div>

                    <div class="recording-duration">
                        <span class="recording-duration-icon"></span>
                        <span class="recording-duration-text"><?php echo esc_html__('00:00', 'toeic-practice'); ?></span>
                    </div>

                    <button id="record-btn" 
                        class="btn btn-record ml-auto"  
                        data-paragraph-text="<?php echo esc_attr($item_content); ?>">
                        <span class="record-text"><?php echo esc_html__('Record now', 'toeic-practice'); ?></span>
                    </button>
                </div>
            </div>
            
            <!-- Right Panel: AI Suggestions (1/3 width) -->
            <div class="ai-suggestions-panel">
                <div class="panel-header">
                    <h3><?php echo esc_html__('AI Assistant', 'toeic-practice'); ?></h3>
                    <div class="panel-toggle">
                        <button class="btn btn-sm" id="toggle-suggestions">
                            <span class="toggle-icon">💡</span>
                        </button>
                    </div>
                </div>
                
                <!-- Suggestion list -->
                <div class="suggestions-content">
                    <div class="quick-actions">
                        <h4><?php echo esc_html__('Quick Help', 'toeic-practice'); ?></h4>
                        <div class="action-buttons">
                            <?php foreach ($ai_suggestions as $index => $prompt): ?>
                                <button class="btn btn-sm btn-outline suggestion-btn" 
                                    data-prompt="<?php echo $prompt; ?>" 
                                    >
                                    <?php echo esc_html($prompt); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var topicItemData = {
        topic_id: '<?php echo esc_js($topic_id); ?>',
        item_id: '<?php echo esc_js($item_id); ?>',
        item_type: '<?php echo esc_js($item_type); ?>'
    };
</script>

<?php include_once TOEIC_PRACTICE_PLUGIN_DIR . 'templates/parts/footer.php'; ?>